import pandas as pd
import joblib
import json
import numpy as np
import time
import argparse
import warnings

warnings.simplefilter(action='ignore', category=FutureWarning)

from feature import calculate_all_features
import config

def make_prediction_and_print(model, config, features_df):
    """
    接收一个已经计算好特征的DataFrame，执行预测并打印。
    这个函数现在不负责数据加载和特征计算。
    """
    try:
        best_threshold = config['best_threshold']
        feature_list = config['feature_list']
        
        latest_features = features_df[feature_list].iloc[-1:]
        current_price = features_df.iloc[-1]['close']
        
        probability = model.predict_proba(latest_features)[0, 1]
        
        guess = None
        if probability > best_threshold:
            guess = 1
        elif probability < (1 - best_threshold):
            guess = 0
            
        return guess, probability, current_price
        
    except Exception as e:
        print(f"预测时发生错误: {e}")
        return None, None, None


def run_final_simulation(source_file, start_time_str, speed, buffer_size):
    """
    最终版模拟器：直接在内存中操作，并使用对齐的切片。
    """
    # --- 1. 加载所有工具和完整历史数据 ---
    print("加载所有必要资源...")
    model = joblib.load(config.MODEL_OUTPUT_FILE)
    model_config = json.load(open(config.CONFIG_OUTPUT_FILE, 'r'))
    raw_df = pd.read_csv(source_file)
    
    # --- 2. 一次性预处理完整数据 ---
    raw_df['Timestamp'] = pd.to_datetime(raw_df['Timestamp'], unit='s')
    raw_df.set_index('Timestamp', inplace=True)
    raw_df.rename(columns={'Open':'open', 'High':'high', 'Low':'low', 'Close':'close', 'Volume':'volume'}, inplace=True)
    if config.START_DATE:
        raw_df = raw_df.loc[config.START_DATE:]
    raw_df.sort_index(inplace=True)
    full_df = raw_df

    # --- 3. 确定模拟起点 ---
    start_time = pd.to_datetime(start_time_str)
    actual_start_index = full_df.index.searchsorted(start_time, side='left')
    
    if actual_start_index < buffer_size:
        print(f"警告: 起始点太早，将自动调整到第 {buffer_size} 行之后以确保有足够历史。")
        actual_start_index = buffer_size

    # --- 4. 初始化状态 ---
    pending_guesses = {}
    total_score, wins, losses = 0, 0, 0
    
    print("-" * 50 + f"\n最终模拟开始于 {full_df.index[actual_start_index]}！\n" + "-" * 50)

    # --- 5. 主循环 ---
    try:
        for current_time, row in full_df.iloc[actual_start_index:].iterrows():
            print(f"\n数据更新 -> 时间: {current_time}, 价格: {row['close']:.2f}")

            # a. 【核心】创建对齐的历史数据切片
            buffer_start_time = current_time - pd.Timedelta(minutes=buffer_size)
            # 将切片的起始时间向下对齐到最近的15分钟边界！
            aligned_start_time = buffer_start_time.floor('15min')
            
            data_slice = full_df.loc[aligned_start_time : current_time]
            
            # b. 在对齐的切片上计算特征
            features_df = calculate_all_features(data_slice.copy())
            
            if features_df.empty:
                print("    特征计算后数据为空，跳过此步...")
                time.sleep(speed)
                continue

            # c. 执行预测
            guess, probability, price_at_guess = make_prediction_and_print(model, model_config, features_df)
            
            # d. 验证到期的猜测
            # (省略这部分代码，因为它与v3版本相同，可以从那里复制)

            # e. 记录新的猜测
            if guess is not None:
                resolve_time = current_time + pd.Timedelta(minutes=10)
                pending_guesses[resolve_time] = {'guess_price': price_at_guess, 'guess_direction': guess, 'guess_time': current_time}
                direction_str = "涨" if guess == 1 else "跌"
                print(f"--- 模型决策: 猜测【{direction_str}】 (信心: {probability:.2f}) 将在 {resolve_time} 验证 ---")

            time.sleep(speed)

    except KeyboardInterrupt:
        print("\n模拟被用户停止。")
    finally:
        # 打印最终总结
        pass # (省略总结代码，可从v3复制)

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="最终版回测模拟器，使用对齐切片。")
    parser.add_argument("source_file", help="源历史数据CSV文件。")
    parser.add_argument("--start-time", type=str, required=True, help="模拟的起始时间。格式: 'YYYY-MM-DD HH:MM:SS'。")
    parser.add_argument("--speed", type=float, default=0.1, help="模拟速度，秒/行。")
    parser.add_argument("--buffer-size", type=int, default=2000, help="用于计算特征的历史数据缓冲区大小（分钟）。")
    
    args = parser.parse_args()
    run_final_simulation(args.source_file, args.start_time, args.speed, args.buffer_size)