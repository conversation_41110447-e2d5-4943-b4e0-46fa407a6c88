# BTC量化交易项目

## 项目概述

这个项目是一个BTC量化交易系统，包含K线图查看器和机器学习预测模型。项目处理BTC一分钟K线数据，可以展示不同时间周期的K线图，并使用LightGBM模型预测未来10分钟的价格走势。

## 重要更新 (2025-07-10)

**数据泄露问题修复**: 发现并修复了LightGBM模型训练中的严重数据泄露问题。原始训练代码在计算技术指标时使用了未来数据，导致测试和实践中特征值不一致。已创建修复版本的训练和预测脚本。

**时区转换优化**: 修改了`backtestv3.py`，所有时间显示现在都转换为东8区（北京时间），方便中国用户使用。用户输入的时间参数也应该使用北京时间格式。

## 技术栈

- **后端**：Python + Flask + Dash
- **数据可视化**：Plotly
- **数据处理**：Pandas
- **机器学习**：LightGBM + Scikit-learn
- **特征工程**：技术指标计算（ATR、移动平均、RSI等）

## 项目结构

```
btc-quant/
├── .venv/                          # Python虚拟环境
├── app.py                          # K线图查看器主应用
├── btcusd_1-min_data.csv          # BTC 1分钟K线数据
├── data.py                         # 数据下载脚本
├── project.md                      # 项目文档
├── run.sh                          # 启动脚本
├── LightGBM.py                     # 原始训练脚本（存在数据泄露）
├── LightGBM_fixed.py              # 修复版训练脚本（推荐使用）
├── yuce.py                         # 原始预测脚本
├── yuce_fixed.py                   # 修复版预测脚本（推荐使用）
├── compare_models.py               # 模型对比脚本
├── feature.py                      # 特征计算函数
├── backtest.py                     # 回测脚本
├── btc_prediction_model.joblib     # 原始模型文件
├── btc_prediction_model_fixed.joblib # 修复版模型文件
├── model_config.json               # 原始模型配置
└── model_config_fixed.json         # 修复版模型配置
```

## 核心流程

1. **数据加载**：
   - 从CSV文件加载1分钟K线数据
   - 将时间戳转换为日期时间格式

2. **数据聚合**：
   - 根据选定的时间周期（1分钟、15分钟、30分钟、1小时、4小时、1天）聚合数据
   - 聚合规则：
     - Open: 取时间段内第一个值
     - High: 取时间段内最大值
     - Low: 取时间段内最小值
     - Close: 取时间段内最后一个值
     - Volume: 取时间段内所有值的总和

3. **数据可视化**：
   - 使用Plotly创建交互式K线图
   - 提供时间周期选择功能
   - 提供日期范围选择功能

## 数据结构

原始CSV数据结构：
```
Timestamp,Open,High,Low,Close,Volume
1325412060.0,4.58,4.58,4.58,4.58,0.0
1325412120.0,4.58,4.58,4.58,4.58,0.0
...
```

内部处理后的数据结构：
```python
DataFrame(
    index=DatetimeIndex,  # 时间戳转换为日期时间索引
    data={
        'Open': float,    # 开盘价
        'High': float,    # 最高价
        'Low': float,     # 最低价
        'Close': float,   # 收盘价
        'Volume': float   # 交易量
    }
)
```

## 使用说明

1. 确保已安装所有依赖：
   ```
   pip install flask pandas plotly dash
   ```

2. 运行应用程序：
   ```
   ./run.sh
   ```

3. 在浏览器中访问：
   ```
   http://127.0.0.1:8050/
   ```

4. 使用界面控件选择不同的时间周期和日期范围来查看K线图。

## 前端选择说明

对于本项目，我们选择了基于Python的Dash框架作为前端解决方案，主要基于以下考虑：

1. **数据处理需求**：项目需要对大量的1分钟K线数据进行聚合处理，Python的Pandas库非常适合这类数据处理任务。

2. **快速开发**：Dash结合Plotly提供了快速构建数据可视化应用的能力，无需编写大量的HTML、CSS和JavaScript代码。

3. **交互性**：Dash提供了丰富的交互式组件，如下拉菜单、日期选择器等，使用户可以方便地选择不同的时间周期和日期范围。

4. **专业图表**：Plotly提供了专业的金融图表组件，包括K线图、成交量图等，非常适合金融数据的可视化。

5. **一体化解决方案**：使用Dash可以在一个Python应用中同时处理后端逻辑和前端展示，简化了开发流程。

如果未来需要更专业的交易界面或更复杂的前端功能，可以考虑迁移到基于React的解决方案，并使用TradingView的Lightweight Charts或ECharts等专业金融图表库。
