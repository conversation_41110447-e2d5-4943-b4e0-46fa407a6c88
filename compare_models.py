import pandas as pd
import numpy as np
import joblib
import json
from datetime import datetime

print("="*80)
print("模型对比脚本 - 验证数据泄露修复效果")
print("="*80)

def load_test_data():
    """加载测试数据"""
    print("加载测试数据...")
    df = pd.read_csv('btcusd_1-min_data.csv')
    df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s')
    df.set_index('Timestamp', inplace=True)
    df.rename(columns={
        'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close', 'Volume': 'volume'
    }, inplace=True)
    df.sort_index(inplace=True)
    
    # 选择最近的一段数据进行测试
    test_data = df.tail(1000)  # 最近1000条数据
    print(f"测试数据范围: {test_data.index.min()} 到 {test_data.index.max()}")
    return test_data

def calculate_features_old_way(df):
    """使用原始方法计算特征（存在数据泄露）"""
    print("使用原始方法计算特征...")
    
    # 这里复制原始的特征计算逻辑
    epsilon = 1e-9
    
    # 价格/动量特征
    for n in [1, 3, 5, 10, 30, 60]:
        df[f'return_{n}min'] = df['close'].pct_change(n)
    
    # 趋势特征
    for n in [10, 30, 60]:
        df[f'sma_{n}'] = df['close'].rolling(window=n).mean()
        df[f'price_div_sma_{n}'] = df['close'] / (df[f'sma_{n}'] + epsilon)
    df['sma_10_div_sma_30'] = df['sma_10'] / (df['sma_30'] + epsilon)
    
    # 成交量特征
    for n in [10, 30, 60]:
        df[f'vma_{n}'] = df['volume'].rolling(window=n).mean()
        df[f'volume_div_vma_{n}'] = df['volume'] / (df[f'vma_{n}'] + epsilon)
    
    # VWAP特征
    df['price_x_volume'] = df['close'] * df['volume']
    vwap_numerator = df['price_x_volume'].rolling(window=30).sum()
    vwap_denominator = df['volume'].rolling(window=30).sum()
    df['vwap_30'] = vwap_numerator / (vwap_denominator + epsilon)
    df['price_div_vwap_30'] = df['close'] / (df['vwap_30'] + epsilon)
    df.drop('price_x_volume', axis=1, inplace=True)
    
    # 时间特征
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    
    # K线特征
    df = get_standardized_kline_features_old(df, timeframe_suffix='_1m', epsilon=epsilon)
    
    # 滚动特征
    for window in [3, 5]:
        if f'body_percent_of_range_1m' in df.columns:
            df[f'body_percent_mean_{window}m'] = df['body_percent_of_range_1m'].rolling(window=window).mean()
        if f'range_norm_by_atr_1m' in df.columns:
            df[f'range_norm_by_atr_mean_{window}m'] = df['range_norm_by_atr_1m'].rolling(window=window).mean()
    
    return df

def get_standardized_kline_features_old(df, timeframe_suffix='', epsilon=1e-9):
    """原始的K线特征计算"""
    try:
        high_low = df['high'] - df['low']
        high_prev_close = abs(df['high'] - df['close'].shift(1))
        low_prev_close = abs(df['low'] - df['close'].shift(1))
        tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)
        atr_col = f'atr_14{timeframe_suffix}'
        df[atr_col] = tr.rolling(window=14).mean()

        body_size = abs(df['close'] - df['open'])
        price_range = df['high'] - df['low']
        upper_shadow = df['high'] - df[['open', 'close']].max(axis=1)
        lower_shadow = df[['open', 'close']].min(axis=1) - df['low']
        
        df[f'body_percent_of_range{timeframe_suffix}'] = body_size / (price_range + epsilon)
        df[f'upper_shadow_percent_of_range{timeframe_suffix}'] = upper_shadow / (price_range + epsilon)
        df[f'lower_shadow_percent_of_range{timeframe_suffix}'] = lower_shadow / (price_range + epsilon)
        df[f'range_norm_by_atr{timeframe_suffix}'] = price_range / (df[atr_col] + epsilon)
        
    except Exception as e:
        print(f"K线特征计算出错: {e}")
    
    return df

def calculate_features_new_way(df, target_index):
    """使用新方法计算特征（无数据泄露）"""
    print(f"使用新方法计算特征（目标时间点: {target_index}）...")
    
    # 只使用目标时间点及之前的数据
    historical_data = df.loc[:target_index].copy()
    
    epsilon = 1e-9
    
    # 价格/动量特征
    for n in [1, 3, 5, 10, 30, 60]:
        historical_data[f'return_{n}min'] = historical_data['close'].pct_change(n)
    
    # 趋势特征
    for n in [10, 30, 60]:
        historical_data[f'sma_{n}'] = historical_data['close'].rolling(window=n).mean()
        historical_data[f'price_div_sma_{n}'] = historical_data['close'] / (historical_data[f'sma_{n}'] + epsilon)
    historical_data['sma_10_div_sma_30'] = historical_data['sma_10'] / (historical_data['sma_30'] + epsilon)
    
    # 成交量特征
    for n in [10, 30, 60]:
        historical_data[f'vma_{n}'] = historical_data['volume'].rolling(window=n).mean()
        historical_data[f'volume_div_vma_{n}'] = historical_data['volume'] / (historical_data[f'vma_{n}'] + epsilon)
    
    # VWAP特征
    historical_data['price_x_volume'] = historical_data['close'] * historical_data['volume']
    vwap_numerator = historical_data['price_x_volume'].rolling(window=30).sum()
    vwap_denominator = historical_data['volume'].rolling(window=30).sum()
    historical_data['vwap_30'] = vwap_numerator / (vwap_denominator + epsilon)
    historical_data['price_div_vwap_30'] = historical_data['close'] / (historical_data['vwap_30'] + epsilon)
    historical_data.drop('price_x_volume', axis=1, inplace=True)
    
    # 时间特征
    historical_data['hour'] = historical_data.index.hour
    historical_data['day_of_week'] = historical_data.index.dayofweek
    
    # K线特征
    historical_data = get_standardized_kline_features_old(historical_data, timeframe_suffix='_1m', epsilon=epsilon)
    
    # 滚动特征
    for window in [3, 5]:
        if f'body_percent_of_range_1m' in historical_data.columns:
            historical_data[f'body_percent_mean_{window}m'] = historical_data['body_percent_of_range_1m'].rolling(window=window).mean()
        if f'range_norm_by_atr_1m' in historical_data.columns:
            historical_data[f'range_norm_by_atr_mean_{window}m'] = historical_data['range_norm_by_atr_1m'].rolling(window=window).mean()
    
    return historical_data

def compare_features_at_timepoint(df, target_timestamp):
    """比较特定时间点的特征差异"""
    print(f"\n比较时间点 {target_timestamp} 的特征差异:")
    print("-" * 60)
    
    # 方法1：原始方法（全部数据）
    df_old = calculate_features_old_way(df.copy())
    df_old_clean = df_old.dropna()
    
    if target_timestamp not in df_old_clean.index:
        print(f"错误：目标时间点在原始方法中不存在")
        return None
    
    features_old = df_old_clean.loc[target_timestamp]
    
    # 方法2：新方法（只用历史数据）
    df_new = calculate_features_new_way(df.copy(), target_timestamp)
    df_new_clean = df_new.dropna()
    
    if target_timestamp not in df_new_clean.index:
        print(f"错误：目标时间点在新方法中不存在")
        return None
    
    features_new = df_new_clean.loc[target_timestamp]
    
    # 比较关键特征
    key_features = ['return_10min', 'sma_30', 'price_div_sma_30', 'atr_14_1m', 
                   'body_percent_of_range_1m', 'vwap_30', 'price_div_vwap_30']
    
    print(f"{'特征名称':<25} {'原始方法':<15} {'修复方法':<15} {'差异':<15} {'差异%':<10}")
    print("-" * 80)
    
    differences = {}
    for feature in key_features:
        if feature in features_old.index and feature in features_new.index:
            old_val = features_old[feature]
            new_val = features_new[feature]
            
            if pd.isna(old_val) or pd.isna(new_val):
                diff = np.nan
                diff_pct = np.nan
            else:
                diff = abs(old_val - new_val)
                diff_pct = (diff / abs(old_val) * 100) if old_val != 0 else np.nan
            
            differences[feature] = {
                'old': old_val,
                'new': new_val,
                'diff': diff,
                'diff_pct': diff_pct
            }
            
            print(f"{feature:<25} {old_val:<15.6f} {new_val:<15.6f} {diff:<15.6f} {diff_pct:<10.2f}")
    
    return differences

def main():
    """主函数"""
    # 加载测试数据
    test_data = load_test_data()
    
    # 选择几个测试时间点
    test_timestamps = test_data.index[-100::20]  # 选择最后100条数据中的每20条
    
    print(f"\n将测试 {len(test_timestamps)} 个时间点的特征差异")
    
    all_differences = {}
    
    for i, timestamp in enumerate(test_timestamps):
        print(f"\n{'='*20} 测试点 {i+1}/{len(test_timestamps)} {'='*20}")
        differences = compare_features_at_timepoint(test_data, timestamp)
        if differences:
            all_differences[timestamp] = differences
    
    # 汇总统计
    print(f"\n{'='*20} 汇总统计 {'='*20}")
    
    if all_differences:
        feature_names = list(next(iter(all_differences.values())).keys())
        
        for feature in feature_names:
            diffs = [all_differences[ts][feature]['diff'] for ts in all_differences 
                    if not pd.isna(all_differences[ts][feature]['diff'])]
            
            if diffs:
                avg_diff = np.mean(diffs)
                max_diff = np.max(diffs)
                min_diff = np.min(diffs)
                
                print(f"{feature}:")
                print(f"  平均差异: {avg_diff:.6f}")
                print(f"  最大差异: {max_diff:.6f}")
                print(f"  最小差异: {min_diff:.6f}")
                print()
    
    print("对比完成！")
    print("\n结论:")
    print("1. 如果差异很大，说明原始方法确实存在数据泄露问题")
    print("2. 修复后的方法只使用历史数据，避免了前瞻偏差")
    print("3. 实际交易中应该使用修复后的方法")

if __name__ == '__main__':
    main()
