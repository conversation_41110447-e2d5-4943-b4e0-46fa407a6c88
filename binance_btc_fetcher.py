#!/usr/bin/env python3
"""
Binance BTC价格获取脚本
实时获取BTC/USDT的1分钟K线数据并保存到CSV文件
"""

import requests
import pandas as pd
import time
import json
from datetime import datetime, timezone
import os
import signal
import sys

class BinanceBTCFetcher:
    def __init__(self, symbol='BTCUSDT', interval='1m', output_file='live_btc_data.csv'):
        """
        初始化Binance BTC数据获取器

        Args:
            symbol: 交易对符号，默认BTCUSDT
            interval: K线间隔，默认1m（1分钟）
            output_file: 输出CSV文件名
        """
        self.symbol = symbol
        self.interval = interval
        self.output_file = output_file
        self.base_url = 'https://api.binance.com'
        self.running = True

        # 设置信号处理器，优雅退出
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        print(f"🚀 Binance BTC数据获取器启动")
        print(f"📊 交易对: {self.symbol}")
        print(f"⏱️  间隔: {self.interval}")
        print(f"📁 输出文件: {self.output_file}")
        print(f"🌐 API地址: {self.base_url}")
        print("按 Ctrl+C 停止获取\n")

    def signal_handler(self, signum, frame):
        """信号处理器，优雅退出"""
        print(f"\n📋 接收到退出信号 {signum}，正在停止...")
        self.running = False
        # 强制退出，避免卡住
        import sys
        sys.exit(0)

    def get_server_time(self):
        """获取Binance服务器时间"""
        try:
            url = f"{self.base_url}/api/v3/time"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            return response.json()['serverTime']
        except Exception as e:
            print(f"❌ 获取服务器时间失败: {e}")
            return None

    def get_latest_kline(self):
        """获取最新的K线数据"""
        try:
            url = f"{self.base_url}/api/v3/klines"
            params = {
                'symbol': self.symbol,
                'interval': self.interval,
                'limit': 1  # 只获取最新的一条
            }

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()

            klines = response.json()
            if not klines:
                return None

            # 解析K线数据
            kline = klines[0]
            data = {
                'Timestamp': int(kline[0]) // 1000,  # 转换为秒级时间戳
                'Open': float(kline[1]),
                'High': float(kline[2]),
                'Low': float(kline[3]),
                'Close': float(kline[4]),
                'Volume': float(kline[5]),
                'CloseTime': int(kline[6]) // 1000,
                'QuoteVolume': float(kline[7]),
                'TradeCount': int(kline[8]),
                'TakerBuyBaseVolume': float(kline[9]),
                'TakerBuyQuoteVolume': float(kline[10])
            }

            return data

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 解析数据失败: {e}")
            return None

    def save_to_csv(self, data, is_first=False):
        """保存数据到CSV文件"""
        try:
            df = pd.DataFrame([data])

            if is_first or not os.path.exists(self.output_file):
                # 第一次写入或文件不存在，写入表头
                df.to_csv(self.output_file, index=False, mode='w')
                print(f"📁 创建新文件: {self.output_file}")
            else:
                # 追加模式
                df.to_csv(self.output_file, index=False, mode='a', header=False)

            return True

        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return False

    def format_timestamp(self, timestamp):
        """格式化时间戳为可读时间"""
        dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
        # 转换为北京时间
        beijing_dt = dt.astimezone(timezone.utc).replace(tzinfo=None) + pd.Timedelta(hours=8)
        return beijing_dt.strftime('%Y-%m-%d %H:%M:%S')

    def display_data(self, data):
        """显示数据"""
        timestamp_str = self.format_timestamp(data['Timestamp'])
        print(f"⏰ {timestamp_str} | "
              f"💰 ${data['Close']:8.2f} | "
              f"📈 ${data['High']:8.2f} | "
              f"📉 ${data['Low']:8.2f} | "
              f"📊 {data['Volume']:8.2f}")

    def check_connection(self):
        """检查网络连接"""
        try:
            server_time = self.get_server_time()
            if server_time:
                print(f"✅ 连接正常，服务器时间: {self.format_timestamp(server_time/1000)}")
                return True
            else:
                print("❌ 连接失败")
                return False
        except Exception as e:
            print(f"❌ 连接检查失败: {e}")
            return False

    def run(self, update_interval=60):
        """
        运行数据获取循环

        Args:
            update_interval: 更新间隔（秒），默认60秒
        """
        print("🔍 检查网络连接...")
        if not self.check_connection():
            print("❌ 无法连接到Binance API，请检查网络连接")
            return

        print(f"🔄 开始获取数据，每{update_interval}秒更新一次")
        print("-" * 80)

        last_timestamp = None
        is_first = True

        while self.running:
            try:
                # 获取最新K线数据
                data = self.get_latest_kline()

                if data is None:
                    print("⚠️  获取数据失败，等待重试...")
                    time.sleep(5)
                    continue

                # 检查是否是新数据
                if last_timestamp is None or data['Timestamp'] != last_timestamp:
                    # 显示数据
                    self.display_data(data)

                    # 保存到CSV
                    if self.save_to_csv(data, is_first):
                        last_timestamp = data['Timestamp']
                        is_first = False
                    else:
                        print("⚠️  保存数据失败")

                # 等待下次更新
                time.sleep(update_interval)

            except KeyboardInterrupt:
                print("\n📋 用户中断，正在退出...")
                break
            except Exception as e:
                print(f"❌ 运行时错误: {e}")
                print("⏳ 等待5秒后重试...")
                time.sleep(5)

        print(f"\n✅ 数据获取已停止")
        print(f"📁 数据已保存到: {self.output_file}")

    def get_historical_data(self, limit=1000):
        """获取历史数据用于初始化（支持大量数据分批获取）"""
        try:
            print(f"📚 获取历史数据 (目标{limit}条记录)...")

            # Binance API单次最大限制通常是1000或1500
            max_limit_per_request = 1000
            all_data = []

            if limit <= max_limit_per_request:
                # 单次请求就够了
                print(f"🔄 单次获取 {limit} 条记录...")
                klines = self._fetch_klines(limit=limit)
                if klines:
                    all_data.extend(klines)
            else:
                # 需要分批获取
                remaining = limit
                end_time = None  # 从最新开始
                batch_count = 0

                while remaining > 0 and batch_count < 50:  # 最多50批，防止无限循环
                    batch_count += 1
                    current_limit = min(remaining, max_limit_per_request)

                    print(f"🔄 第{batch_count}批: 获取 {current_limit} 条记录...")

                    klines = self._fetch_klines(limit=current_limit, end_time=end_time)
                    if not klines:
                        print(f"⚠️  第{batch_count}批获取失败，停止")
                        break

                    # 插入到开头（因为我们是从最新往前获取）
                    all_data = klines + all_data
                    remaining -= len(klines)

                    # 设置下一批的结束时间为当前批最早的时间
                    end_time = int(klines[0][0])  # 第一条记录的开始时间

                    print(f"✅ 第{batch_count}批完成，已获取 {len(all_data)} 条记录")

                    # 避免请求过快
                    time.sleep(0.1)

            if not all_data:
                print("❌ 没有获取到任何数据")
                return False

            # 转换数据格式
            data_list = []
            for kline in all_data:
                data = {
                    'Timestamp': int(kline[0]) // 1000,
                    'Open': float(kline[1]),
                    'High': float(kline[2]),
                    'Low': float(kline[3]),
                    'Close': float(kline[4]),
                    'Volume': float(kline[5]),
                    'CloseTime': int(kline[6]) // 1000,
                    'QuoteVolume': float(kline[7]),
                    'TradeCount': int(kline[8]),
                    'TakerBuyBaseVolume': float(kline[9]),
                    'TakerBuyQuoteVolume': float(kline[10])
                }
                data_list.append(data)

            # 按时间排序（确保从旧到新）
            data_list.sort(key=lambda x: x['Timestamp'])

            # 保存历史数据
            df = pd.DataFrame(data_list)
            df.to_csv(self.output_file, index=False)

            print(f"✅ 历史数据已保存: {len(data_list)} 条记录")
            print(f"📅 时间范围: {self.format_timestamp(data_list[0]['Timestamp'])} 到 {self.format_timestamp(data_list[-1]['Timestamp'])}")

            return True

        except Exception as e:
            print(f"❌ 获取历史数据失败: {e}")
            return False

    def _fetch_klines(self, limit=1000, end_time=None):
        """获取单批K线数据"""
        try:
            url = f"{self.base_url}/api/v3/klines"
            params = {
                'symbol': self.symbol,
                'interval': self.interval,
                'limit': limit
            }

            if end_time:
                params['endTime'] = end_time

            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()

            return response.json()

        except Exception as e:
            print(f"❌ 获取K线数据失败: {e}")
            return None

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="Binance BTC价格获取脚本")
    parser.add_argument("--symbol", default="BTCUSDT", help="交易对符号 (默认: BTCUSDT)")
    parser.add_argument("--interval", default="1m", help="K线间隔 (默认: 1m)")
    parser.add_argument("--output", default="live_btc_data.csv", help="输出文件名 (默认: live_btc_data.csv)")
    parser.add_argument("--update-interval", type=int, default=60, help="更新间隔秒数 (默认: 60)")
    parser.add_argument("--historical", type=int, default=0, help="获取历史数据条数 (默认: 0, 不获取)")

    args = parser.parse_args()

    # 创建获取器
    fetcher = BinanceBTCFetcher(
        symbol=args.symbol,
        interval=args.interval,
        output_file=args.output
    )

    # 如果指定了历史数据，先获取历史数据
    if args.historical > 0:
        if not fetcher.get_historical_data(args.historical):
            print("❌ 获取历史数据失败，退出")
            return

    # 开始实时获取
    fetcher.run(args.update_interval)

if __name__ == "__main__":
    main()

# 使用示例：
#
# 基本用法：
# python binance_btc_fetcher.py
#
# 获取历史数据并实时更新：
# python binance_btc_fetcher.py --historical 1000 --update-interval 30
#
# 自定义交易对和输出文件：
# python binance_btc_fetcher.py --symbol ETHUSDT --output eth_data.csv
#
# 快速更新模式（每10秒）：
# python binance_btc_fetcher.py --update-interval 10
