#!/usr/bin/env python3
"""
Binance BTC WebSocket实时数据获取脚本
使用WebSocket连接实时获取BTC/USDT的K线数据
"""

import websocket
import json
import pandas as pd
import time
from datetime import datetime, timezone
import threading
import signal
import sys
import os

class BinanceWebSocketFetcher:
    def __init__(self, symbol='btcusdt', interval='1m', output_file='live_btc_data.csv'):
        """
        初始化Binance WebSocket数据获取器

        Args:
            symbol: 交易对符号，默认btcusdt（小写）
            interval: K线间隔，默认1m
            output_file: 输出CSV文件名
        """
        self.symbol = symbol.lower()
        self.interval = interval
        self.output_file = output_file
        self.ws_url = f"wss://stream.binance.com:9443/ws/{self.symbol}@kline_{self.interval}"
        self.ws = None
        self.running = True
        self.last_timestamp = None

        # 线程锁，用于文件写入
        self.file_lock = threading.Lock()

        # 设置信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        print(f"🚀 Binance WebSocket BTC数据获取器启动")
        print(f"📊 交易对: {self.symbol.upper()}")
        print(f"⏱️  间隔: {self.interval}")
        print(f"📁 输出文件: {self.output_file}")
        print(f"🌐 WebSocket地址: {self.ws_url}")
        print("按 Ctrl+C 停止获取\n")

    def signal_handler(self, signum, frame):
        """信号处理器，优雅退出"""
        print(f"\n📋 接收到退出信号 {signum}，正在停止...")
        self.running = False
        if self.ws:
            self.ws.close()
        # 强制退出，避免卡住
        import sys
        sys.exit(0)

    def on_message(self, ws, message):
        """WebSocket消息处理"""
        try:
            data = json.loads(message)

            # 检查是否是K线数据
            if 'k' not in data:
                return

            kline = data['k']

            # 只处理已完成的K线（x=True表示K线已关闭）
            if not kline['x']:
                return

            # 解析K线数据
            kline_data = {
                'Timestamp': int(kline['t']) // 1000,  # 开始时间
                'Open': float(kline['o']),
                'High': float(kline['h']),
                'Low': float(kline['l']),
                'Close': float(kline['c']),
                'Volume': float(kline['v']),
                'CloseTime': int(kline['T']) // 1000,  # 结束时间
                'QuoteVolume': float(kline['q']),
                'TradeCount': int(kline['n']),
                'TakerBuyBaseVolume': float(kline['V']),
                'TakerBuyQuoteVolume': float(kline['Q'])
            }

            # 检查是否是新数据
            if self.last_timestamp is None or kline_data['Timestamp'] != self.last_timestamp:
                self.display_data(kline_data)
                self.save_to_csv(kline_data)
                self.last_timestamp = kline_data['Timestamp']

        except Exception as e:
            print(f"❌ 处理消息失败: {e}")

    def on_error(self, ws, error):
        """WebSocket错误处理"""
        print(f"❌ WebSocket错误: {error}")

    def on_close(self, ws, close_status_code, close_msg):
        """WebSocket关闭处理"""
        print(f"📋 WebSocket连接已关闭: {close_status_code} - {close_msg}")

    def on_open(self, ws):
        """WebSocket连接建立"""
        print("✅ WebSocket连接已建立")
        print("-" * 80)

    def save_to_csv(self, data):
        """保存数据到CSV文件（线程安全）"""
        try:
            with self.file_lock:
                df = pd.DataFrame([data])

                if not os.path.exists(self.output_file):
                    # 文件不存在，创建新文件
                    df.to_csv(self.output_file, index=False, mode='w')
                    print(f"📁 创建新文件: {self.output_file}")
                else:
                    # 追加模式
                    df.to_csv(self.output_file, index=False, mode='a', header=False)

            return True

        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return False

    def format_timestamp(self, timestamp):
        """格式化时间戳为北京时间"""
        dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
        beijing_dt = dt.astimezone(timezone.utc).replace(tzinfo=None) + pd.Timedelta(hours=8)
        return beijing_dt.strftime('%Y-%m-%d %H:%M:%S')

    def display_data(self, data):
        """显示数据"""
        timestamp_str = self.format_timestamp(data['Timestamp'])
        print(f"⏰ {timestamp_str} | "
              f"💰 ${data['Close']:8.2f} | "
              f"📈 ${data['High']:8.2f} | "
              f"📉 ${data['Low']:8.2f} | "
              f"📊 {data['Volume']:8.2f}")

    def run(self):
        """运行WebSocket连接"""
        try:
            # 启用WebSocket调试（可选）
            # websocket.enableTrace(True)

            self.ws = websocket.WebSocketApp(
                self.ws_url,
                on_open=self.on_open,
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close
            )

            # 运行WebSocket
            self.ws.run_forever()

        except Exception as e:
            print(f"❌ WebSocket运行失败: {e}")
        finally:
            print(f"\n✅ 数据获取已停止")
            print(f"📁 数据已保存到: {self.output_file}")

    def get_historical_data(self, limit=1000):
        """获取历史数据用于初始化（支持大量数据分批获取）"""
        try:
            import requests

            print(f"📚 获取历史数据 (目标{limit}条记录)...")

            # Binance API单次最大限制通常是1000或1500
            max_limit_per_request = 1000
            all_data = []

            if limit <= max_limit_per_request:
                # 单次请求就够了
                print(f"🔄 单次获取 {limit} 条记录...")
                klines = self._fetch_klines_ws(limit=limit)
                if klines:
                    all_data.extend(klines)
            else:
                # 需要分批获取
                remaining = limit
                end_time = None  # 从最新开始
                batch_count = 0

                while remaining > 0 and batch_count < 50:  # 最多50批，防止无限循环
                    batch_count += 1
                    current_limit = min(remaining, max_limit_per_request)

                    print(f"🔄 第{batch_count}批: 获取 {current_limit} 条记录...")

                    klines = self._fetch_klines_ws(limit=current_limit, end_time=end_time)
                    if not klines:
                        print(f"⚠️  第{batch_count}批获取失败，停止")
                        break

                    # 插入到开头（因为我们是从最新往前获取）
                    all_data = klines + all_data
                    remaining -= len(klines)

                    # 设置下一批的结束时间为当前批最早的时间
                    end_time = int(klines[0][0])  # 第一条记录的开始时间

                    print(f"✅ 第{batch_count}批完成，已获取 {len(all_data)} 条记录")

                    # 避免请求过快
                    time.sleep(0.1)

            if not all_data:
                print("❌ 没有获取到任何数据")
                return False

            # 转换数据格式
            data_list = []
            for kline in all_data:
                data = {
                    'Timestamp': int(kline[0]) // 1000,
                    'Open': float(kline[1]),
                    'High': float(kline[2]),
                    'Low': float(kline[3]),
                    'Close': float(kline[4]),
                    'Volume': float(kline[5]),
                    'CloseTime': int(kline[6]) // 1000,
                    'QuoteVolume': float(kline[7]),
                    'TradeCount': int(kline[8]),
                    'TakerBuyBaseVolume': float(kline[9]),
                    'TakerBuyQuoteVolume': float(kline[10])
                }
                data_list.append(data)

            # 按时间排序（确保从旧到新）
            data_list.sort(key=lambda x: x['Timestamp'])

            # 保存历史数据
            df = pd.DataFrame(data_list)
            df.to_csv(self.output_file, index=False)

            print(f"✅ 历史数据已保存: {len(data_list)} 条记录")
            print(f"📅 时间范围: {self.format_timestamp(data_list[0]['Timestamp'])} 到 {self.format_timestamp(data_list[-1]['Timestamp'])}")

            return True

        except Exception as e:
            print(f"❌ 获取历史数据失败: {e}")
            return False

    def _fetch_klines_ws(self, limit=1000, end_time=None):
        """获取单批K线数据（WebSocket版本）"""
        try:
            import requests

            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': self.symbol.upper(),
                'interval': self.interval,
                'limit': limit
            }

            if end_time:
                params['endTime'] = end_time

            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()

            return response.json()

        except Exception as e:
            print(f"❌ 获取K线数据失败: {e}")
            return None

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="Binance BTC WebSocket实时数据获取脚本")
    parser.add_argument("--symbol", default="BTCUSDT", help="交易对符号 (默认: BTCUSDT)")
    parser.add_argument("--interval", default="1m", help="K线间隔 (默认: 1m)")
    parser.add_argument("--output", default="live_btc_data.csv", help="输出文件名 (默认: live_btc_data.csv)")
    parser.add_argument("--historical", type=int, default=0, help="获取历史数据条数 (默认: 0, 不获取)")

    args = parser.parse_args()

    # 创建获取器
    fetcher = BinanceWebSocketFetcher(
        symbol=args.symbol,
        interval=args.interval,
        output_file=args.output
    )

    # 如果指定了历史数据，先获取历史数据
    if args.historical > 0:
        if not fetcher.get_historical_data(args.historical):
            print("❌ 获取历史数据失败，但继续实时获取")

    # 开始实时获取
    fetcher.run()

if __name__ == "__main__":
    main()

# 使用示例：
#
# 基本用法（实时获取BTC数据）：
# python binance_btc_websocket.py
#
# 获取历史数据并实时更新：
# python binance_btc_websocket.py --historical 1000
#
# 自定义交易对：
# python binance_btc_websocket.py --symbol ETHUSDT --output eth_data.csv
#
# 获取5分钟K线：
# python binance_btc_websocket.py --interval 5m
