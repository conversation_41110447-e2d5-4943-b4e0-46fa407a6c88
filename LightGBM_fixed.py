import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import lightgbm as lgb
from sklearn.calibration import CalibratedClassifierCV
import joblib
import json

print("开始修复数据泄露问题的训练脚本...")

# ===================================================================
#      第1部分: 数据加载和预处理
# ===================================================================

# 加载数据
df = pd.read_csv('btcusd_1-min_data.csv')

# 将Unix时间戳转换为日期时间格式，并设为索引
df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s') 
df.set_index('Timestamp', inplace=True)

# 筛选数据范围（避免使用过多数据）
three_years_ago = datetime.now() - timedelta(days=10)
df_recent = df.loc[three_years_ago:]
print(f"原始数据范围: {df.index.min()} 到 {df.index.max()}")
print(f"筛选后数据范围: {df_recent.index.min()} 到 {df_recent.index.max()}")
df = df_recent

# 重命名列
df.rename(columns={
    'Open': 'open',
    'High': 'high', 
    'Low': 'low',
    'Close': 'close',
    'Volume': 'volume'
}, inplace=True)

# 检查数据是否有缺失，并按时间排序
df.dropna(inplace=True)
df.sort_index(inplace=True)

# 创建标签（预测10分钟后价格是否上涨）
df['future_price_10min'] = df['close'].shift(-10)
df['label'] = (df['future_price_10min'] > df['close']).astype(int)

print(f"数据预处理完成，共有 {len(df)} 条记录")

# ===================================================================
#      第2部分: 无前瞻偏差的特征计算函数
# ===================================================================

def calculate_features_no_lookahead(df, min_history=300):
    """
    计算特征，严格避免前瞻偏差
    对于每个时间点，只使用该时间点及之前的数据计算特征
    """
    print("开始计算无前瞻偏差的特征...")
    
    epsilon = 1e-9
    result_df = df.copy()
    
    # 初始化所有特征列为NaN
    feature_columns = []
    
    # 定义需要计算的特征
    return_windows = [1, 3, 5, 10, 30, 60]
    sma_windows = [10, 30, 60]
    vma_windows = [10, 30, 60]
    
    # 初始化特征列
    for n in return_windows:
        feature_columns.append(f'return_{n}min')
    for n in sma_windows:
        feature_columns.extend([f'sma_{n}', f'price_div_sma_{n}'])
    feature_columns.append('sma_10_div_sma_30')
    for n in vma_windows:
        feature_columns.extend([f'vma_{n}', f'volume_div_vma_{n}'])
    feature_columns.extend(['vwap_30', 'price_div_vwap_30'])
    feature_columns.extend(['hour', 'day_of_week'])
    
    # K线特征
    kline_features = ['atr_14_1m', 'body_percent_of_range_1m', 'upper_shadow_percent_of_range_1m', 
                     'lower_shadow_percent_of_range_1m', 'range_norm_by_atr_1m']
    feature_columns.extend(kline_features)
    
    # 滚动特征
    for window in [3, 5]:
        feature_columns.extend([f'body_percent_mean_{window}m', f'range_norm_by_atr_mean_{window}m'])
    
    # 初始化特征列
    for col in feature_columns:
        result_df[col] = np.nan
    
    print(f"需要计算 {len(feature_columns)} 个特征")
    
    # 逐行计算特征（避免前瞻偏差）
    total_rows = len(df)
    for i in range(min_history, total_rows):
        if i % 10000 == 0:
            print(f"处理进度: {i}/{total_rows} ({i/total_rows*100:.1f}%)")
        
        # 只使用当前时间点及之前的数据
        historical_data = df.iloc[:i+1].copy()
        current_timestamp = df.index[i]
        
        try:
            # 计算基础特征
            features = calculate_single_point_features(historical_data, epsilon)
            
            # 将特征值赋给当前行
            for col in feature_columns:
                if col in features.columns:
                    result_df.loc[current_timestamp, col] = features.iloc[-1][col]
                    
        except Exception as e:
            print(f"计算第 {i} 行特征时出错: {e}")
            continue
    
    print("特征计算完成")
    return result_df

def calculate_single_point_features(historical_data, epsilon=1e-9):
    """
    为单个时间点计算特征，只使用历史数据
    """
    df_work = historical_data.copy()
    
    # 1. 价格/动量特征
    for n in [1, 3, 5, 10, 30, 60]:
        df_work[f'return_{n}min'] = df_work['close'].pct_change(n)
    
    # 2. 趋势特征
    for n in [10, 30, 60]:
        df_work[f'sma_{n}'] = df_work['close'].rolling(window=n).mean()
        df_work[f'price_div_sma_{n}'] = df_work['close'] / (df_work[f'sma_{n}'] + epsilon)
    df_work['sma_10_div_sma_30'] = df_work['sma_10'] / (df_work['sma_30'] + epsilon)
    
    # 3. 成交量特征
    for n in [10, 30, 60]:
        df_work[f'vma_{n}'] = df_work['volume'].rolling(window=n).mean()
        df_work[f'volume_div_vma_{n}'] = df_work['volume'] / (df_work[f'vma_{n}'] + epsilon)
    
    # 4. VWAP特征
    df_work['price_x_volume'] = df_work['close'] * df_work['volume']
    vwap_numerator = df_work['price_x_volume'].rolling(window=30).sum()
    vwap_denominator = df_work['volume'].rolling(window=30).sum()
    df_work['vwap_30'] = vwap_numerator / (vwap_denominator + epsilon)
    df_work['price_div_vwap_30'] = df_work['close'] / (df_work['vwap_30'] + epsilon)
    df_work.drop('price_x_volume', axis=1, inplace=True)
    
    # 5. 时间特征
    df_work['hour'] = df_work.index.hour
    df_work['day_of_week'] = df_work.index.dayofweek
    
    # 6. K线形态特征
    df_work = get_standardized_kline_features_safe(df_work, timeframe_suffix='_1m', epsilon=epsilon)
    
    # 7. 滚动K线特征
    for window in [3, 5]:
        if f'body_percent_of_range_1m' in df_work.columns:
            df_work[f'body_percent_mean_{window}m'] = df_work['body_percent_of_range_1m'].rolling(window=window).mean()
        if f'range_norm_by_atr_1m' in df_work.columns:
            df_work[f'range_norm_by_atr_mean_{window}m'] = df_work['range_norm_by_atr_1m'].rolling(window=window).mean()
    
    return df_work

def get_standardized_kline_features_safe(df, timeframe_suffix='', epsilon=1e-9):
    """
    安全的K线特征计算函数，添加错误处理
    """
    try:
        # 1. 计算ATR
        high_low = df['high'] - df['low']
        high_prev_close = abs(df['high'] - df['close'].shift(1))
        low_prev_close = abs(df['low'] - df['close'].shift(1))
        tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)
        atr_col = f'atr_14{timeframe_suffix}'
        df[atr_col] = tr.rolling(window=14).mean()

        # 2. 计算形态的绝对值
        body_size = abs(df['close'] - df['open'])
        price_range = df['high'] - df['low']
        upper_shadow = df['high'] - df[['open', 'close']].max(axis=1)
        lower_shadow = df[['open', 'close']].min(axis=1) - df['low']
        
        # 3. 标准化
        df[f'body_percent_of_range{timeframe_suffix}'] = body_size / (price_range + epsilon)
        df[f'upper_shadow_percent_of_range{timeframe_suffix}'] = upper_shadow / (price_range + epsilon)
        df[f'lower_shadow_percent_of_range{timeframe_suffix}'] = lower_shadow / (price_range + epsilon)
        df[f'range_norm_by_atr{timeframe_suffix}'] = price_range / (df[atr_col] + epsilon)
        
    except Exception as e:
        print(f"K线特征计算出错: {e}")
    
    return df

# ===================================================================
#      第3部分: 执行特征计算
# ===================================================================

# 计算无前瞻偏差的特征
df_with_features = calculate_features_no_lookahead(df, min_history=300)

# 清理NaN值
df_clean = df_with_features.dropna()
print(f"清理NaN后剩余 {len(df_clean)} 条记录")

# ===================================================================
#      第4部分: 时间序列数据分割
# ===================================================================

# 使用时间序列分割，确保训练集总是在验证集和测试集之前
train_size = int(len(df_clean) * 0.70)
val_size = int(len(df_clean) * 0.15)

# 按时间顺序分割
train_df = df_clean.iloc[:train_size]
val_df = df_clean.iloc[train_size:train_size + val_size]
test_df = df_clean.iloc[train_size + val_size:]

print(f"训练集: {len(train_df)} 条记录 ({train_df.index.min()} 到 {train_df.index.max()})")
print(f"验证集: {len(val_df)} 条记录 ({val_df.index.min()} 到 {val_df.index.max()})")
print(f"测试集: {len(test_df)} 条记录 ({test_df.index.min()} 到 {test_df.index.max()})")

# 定义特征和目标
target = 'label'
excluded_columns = ['open', 'high', 'low', 'close', 'volume', 'label', 'future_price_10min']
excluded_columns += [f'sma_{n}' for n in [10, 30, 60]]
excluded_columns += [f'vma_{n}' for n in [10, 30, 60]]
excluded_columns += ['vwap_30']

features = [col for col in df_clean.columns if col not in excluded_columns]
print(f"使用的特征数量: {len(features)}")

# 准备训练数据
X_train, y_train = train_df[features], train_df[target]
X_val, y_val = val_df[features], val_df[target]
X_test, y_test = test_df[features], test_df[target]

print(f"训练集大小: {len(X_train)}, 验证集大小: {len(X_val)}, 测试集大小: {len(X_test)}")

# ===================================================================
#      第5部分: 模型训练
# ===================================================================

print("开始训练LightGBM模型...")

# 设置模型参数
lgbm = lgb.LGBMClassifier(
    objective='binary',
    metric='auc',
    n_estimators=2000,
    learning_rate=0.05,
    n_jobs=-1,
    random_state=42,
    verbose=-1
)

# 训练模型
lgbm.fit(
    X_train, y_train,
    eval_set=[(X_val, y_val)],
    eval_metric='auc',
    callbacks=[lgb.early_stopping(stopping_rounds=100, verbose=False)]
)

print("基础模型训练完成")

# 概率校准
print("开始概率校准...")
calibrated_model = CalibratedClassifierCV(lgbm, method='isotonic', cv='prefit')
calibrated_model.fit(X_val, y_val)
print("概率校准完成")

# ===================================================================
#      第6部分: 阈值优化
# ===================================================================

print("开始阈值优化...")

# 获取验证集上的校准后概率
val_probabilities = calibrated_model.predict_proba(X_val)[:, 1]

# 设定搜索范围
thresholds_to_test = np.arange(0.56, 0.75, 0.01)

best_score = -np.inf
best_threshold = 0.5

# 遍历所有阈值
for threshold in thresholds_to_test:
    current_score = 0
    trades_made = 0
    
    for i in range(len(val_probabilities)):
        prob = val_probabilities[i]
        actual_result = y_val.iloc[i]
        
        guess = None
        if prob > threshold:
            guess = 1  # 猜涨
            trades_made += 1
        elif prob < (1 - threshold):
            guess = 0  # 猜跌
            trades_made += 1
        
        if guess is not None:
            if guess == actual_result:
                current_score += 0.8  # 猜对
            else:
                current_score += -1.0  # 猜错
    
    if current_score > best_score:
        best_score = current_score
        best_threshold = threshold

print(f"验证集上的最优得分: {best_score:.2f}")
print(f"找到的最优信心阈值: {best_threshold:.2f}")

# ===================================================================
#      第7部分: 保存模型和配置
# ===================================================================

print("保存模型和配置...")

# 保存模型
joblib.dump(calibrated_model, 'btc_prediction_model_fixed.joblib')

# 保存配置
config = {
    'best_threshold': best_threshold,
    'feature_list': features,
    'model_type': 'LightGBM_with_calibration',
    'training_date': datetime.now().isoformat(),
    'data_leakage_fixed': True,
    'train_size': len(X_train),
    'val_size': len(X_val),
    'test_size': len(X_test)
}

with open('model_config_fixed.json', 'w') as f:
    json.dump(config, f, indent=2)

print("模型和配置保存完成")
print(f"模型文件: btc_prediction_model_fixed.joblib")
print(f"配置文件: model_config_fixed.json")

# ===================================================================
#      第8部分: 测试集评估
# ===================================================================

print("\n开始测试集评估...")

test_probabilities = calibrated_model.predict_proba(X_test)[:, 1]
test_score = 0
trades_made = 0
wins = 0
losses = 0
best_threshold = 0.7
for i in range(len(test_probabilities)):
    prob = test_probabilities[i]
    actual_result = y_test.iloc[i]
    
    guess = None
    if prob > best_threshold:
        guess = 1
        trades_made += 1
    elif prob < (1 - best_threshold):
        guess = 0
        trades_made += 1
    
    if guess is not None:
        if guess == actual_result:
            wins += 1
            test_score += 0.8
        else:
            losses += 1
            test_score -= 1.0

print(f"测试集结果:")
print(f"总样本数: {len(test_df)}")
print(f"猜测次数: {trades_made} (占比: {trades_made/len(test_df)*100:.2f}%)")
if trades_made > 0:
    print(f"胜率: {wins/trades_made*100:.2f}%")
print(f"总得分: {test_score:.2f}")

print("\n训练完成！数据泄露问题已修复。")
