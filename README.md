# BTC量化交易系统

一个完整的基于机器学习的BTC价格预测和回测系统，支持实时预测、历史回测、数据获取等功能。

## 📋 目录

- [系统概述](#系统概述)
- [核心组件](#核心组件)
- [快速开始](#快速开始)
- [详细使用指南](#详细使用指南)
- [工作流程](#工作流程)
- [文件说明](#文件说明)
- [最佳实践](#最佳实践)

## 🎯 系统概述

### 预测目标
- **百分比目标**：预测BTC价格先涨1%还是先跌1%
- **时间窗口**：可配置等待时间（默认2-4小时）
- **评分规则**：成功+1分，失败-1分，超时0分

### 核心特性
- 🤖 **机器学习模型**：基于LightGBM的价格预测
- 📊 **实时预测**：持续监控和预测BTC价格
- 🔄 **历史回测**：验证策略在历史数据上的表现
- 📈 **数据获取**：自动获取Binance的实时和历史数据
- 🔔 **报警提醒**：预测信号的声音提醒
- 📝 **详细日志**：完整的预测过程记录

## 🧩 核心组件

### 1. 机器学习模型
- `LightGBM_percentage_v2.py` - 优化版模型训练（20个重要特征）
- `LightGBM_percentage.py` - 原始版模型训练

### 2. 数据获取
- `get_btc_history.py` - 快速获取大量历史数据（推荐）
- `binance_btc_fetcher.py` - REST API版本，支持历史+实时
- `binance_btc_websocket.py` - WebSocket版本，真正实时

### 3. 回测系统
- `backtest_percentage_v4.py` - 限制模式（一次一个预测）
- `backtest_percentage_v2.py` - 并发模式（多个预测）
- `backtestv3.py` / `backtestv4.py` - 原始模型回测

### 4. 实时预测
- `real_time_predictor.py` - 实时预测系统（核心）

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install pandas numpy scikit-learn lightgbm requests

# WebSocket功能（可选）
pip install websocket-client
```

### 2. 获取数据
```bash
# 获取10000条历史数据（约7天）
python get_btc_history.py --limit 10000 --output btcusd_1-min_data.csv
```

### 3. 训练模型
```bash
# 训练优化版模型
python LightGBM_percentage_v2.py --mode train --save-data
```

### 4. 回测验证
```bash
# 运行回测（限制模式，4小时等待）
python backtest_percentage_v4.py btcusd_1-min_data.csv --start-row 5000 --max-wait-hours 4
```

### 5. 实时预测
```bash
# 启动实时预测系统
python real_time_predictor.py --max-wait-hours 4 --update-interval 60
```

## 📖 详细使用指南

### 数据获取工作流

#### 方案1：快速获取（推荐）
```bash
# 获取大量历史数据
python get_btc_history.py --limit 50000 --output btc_data.csv

# 获取其他币种
python get_btc_history.py --symbol ETHUSDT --limit 10000 --output eth_data.csv
```

#### 方案2：持续监控
```bash
# 获取历史数据并持续更新
python binance_btc_fetcher.py --historical 5000 --update-interval 60

# WebSocket实时数据
python binance_btc_websocket.py --historical 1000
```

### 模型训练工作流

#### 完整训练流程
```bash
# 1. 训练模型并保存数据缓存
python LightGBM_percentage_v2.py --mode train --save-data

# 2. 快速验证（使用缓存数据）
python LightGBM_percentage_v2.py --mode validate --load-data

# 3. 查看特征重要性
cat feature_importance_percentage.csv
```

#### 模型文件
- `btc_percentage_model.joblib` - 训练好的模型
- `model_config_percentage.json` - 模型配置
- `processed_data_percentage.pkl` - 预处理数据缓存

### 回测验证工作流

#### 基本回测
```bash
# 限制模式（推荐）
python backtest_percentage_v4.py btcusd_1-min_data.csv --start-row 5000 --max-wait-hours 4

# 并发模式（快速验证）
python backtest_percentage_v2.py btcusd_1-min_data.csv --start-row 5000 --max-wait-hours 2
```

#### 高级回测
```bash
# 指定时间段回测
python backtest_percentage_v4.py btcusd_1-min_data.csv \
    --start-time "2025-07-08 14:30:00" \
    --max-wait-hours 6 \
    --speed 0.1

# 不同等待时间对比
python backtest_percentage_v4.py btcusd_1-min_data.csv --max-wait-hours 1  # 短期
python backtest_percentage_v4.py btcusd_1-min_data.csv --max-wait-hours 8  # 长期
```

### 实时预测工作流

#### 基本使用
```bash
# 默认设置（4小时等待，60秒更新，启用声音）
python real_time_predictor.py

# 自定义设置
python real_time_predictor.py \
    --max-wait-hours 4 \
    --update-interval 30 \
    --log-file my_predictions.log
```

#### 生产环境
```bash
# 服务器运行（禁用声音）
python real_time_predictor.py --no-sound --log-file server_predictions.log

# 后台运行
nohup python real_time_predictor.py --no-sound > output.log 2>&1 &
```

## 🔄 工作流程

### 完整开发流程

```mermaid
graph TD
    A[获取历史数据] --> B[训练模型]
    B --> C[回测验证]
    C --> D{效果满意?}
    D -->|否| E[调整参数]
    E --> B
    D -->|是| F[实时预测]
    F --> G[监控日志]
    G --> H[定期重训练]
    H --> B
```

### 1. 数据准备阶段
```bash
# 获取足够的历史数据
python get_btc_history.py --limit 30000 --output training_data.csv
```

### 2. 模型开发阶段
```bash
# 训练和验证
python LightGBM_percentage_v2.py --mode train --save-data
python LightGBM_percentage_v2.py --mode validate --load-data
```

### 3. 策略验证阶段
```bash
# 多种回测验证
python backtest_percentage_v4.py training_data.csv --max-wait-hours 2
python backtest_percentage_v4.py training_data.csv --max-wait-hours 4
python backtest_percentage_v4.py training_data.csv --max-wait-hours 8
```

### 4. 生产部署阶段
```bash
# 启动实时预测
python real_time_predictor.py --max-wait-hours 4 --no-sound
```

## 📁 文件说明

### 核心脚本

| 文件 | 功能 | 输入 | 输出 |
|------|------|------|------|
| `get_btc_history.py` | 获取历史数据 | API参数 | CSV数据文件 |
| `LightGBM_percentage_v2.py` | 模型训练 | CSV数据 | 模型文件 |
| `backtest_percentage_v4.py` | 回测验证 | CSV数据+模型 | 回测结果 |
| `real_time_predictor.py` | 实时预测 | 模型文件 | 预测日志 |

### 生成文件

| 文件 | 说明 | 用途 |
|------|------|------|
| `btcusd_1-min_data.csv` | BTC历史数据 | 训练和回测 |
| `btc_percentage_model.joblib` | 训练好的模型 | 预测和回测 |
| `model_config_percentage.json` | 模型配置 | 预测参数 |
| `processed_data_percentage.pkl` | 预处理数据缓存 | 快速验证 |
| `prediction_log.txt` | 预测日志 | 分析和监控 |
| `feature_importance_percentage.csv` | 特征重要性 | 模型分析 |

### 配置文件示例

**model_config_percentage.json**:
```json
{
  "best_threshold": 0.660,
  "feature_list": ["hour", "day_of_week", "atr_14_1m", ...],
  "model_type": "LightGBM_percentage_target",
  "target_description": "predict_first_1percent_move"
}
```

## 💡 最佳实践

### 数据管理
- 定期更新历史数据（建议每周）
- 保持至少30天的数据用于训练
- 备份重要的模型和配置文件

### 模型优化
- 定期重新训练模型（建议每月）
- 监控模型性能，及时调整阈值
- 保存不同版本的模型进行对比

### 风险控制
- 使用限制模式避免过度交易
- 设置合理的等待时间
- 监控同时进行的预测数量

### 系统监控
- 定期检查预测日志
- 监控系统错误和网络问题
- 设置合理的更新间隔

### 参数建议

| 场景 | 等待时间 | 更新间隔 | 模式 |
|------|----------|----------|------|
| 快速验证 | 1-2小时 | 30秒 | 并发 |
| 日常交易 | 4小时 | 60秒 | 限制 |
| 保守策略 | 8小时 | 120秒 | 限制 |
| 服务器运行 | 4-6小时 | 60秒 | 限制+无声 |

## 🔧 故障排除

### 常见问题

1. **模型文件不存在**
   ```bash
   # 先训练模型
   python LightGBM_percentage_v2.py --mode train
   ```

2. **网络连接问题**
   ```bash
   # 检查网络连接
   ping api.binance.com
   ```

3. **数据格式错误**
   ```bash
   # 重新获取数据
   python get_btc_history.py --limit 10000
   ```

### 日志分析
```bash
# 查看预测信号
grep "新预测" prediction_log.txt

# 查看成功率
grep -c "成功✅" prediction_log.txt
grep -c "失败❌" prediction_log.txt

# 实时监控
tail -f prediction_log.txt
```

## 📊 性能指标

### 模型评估
- **成功率**：基于有效预测的准确率
- **有效率**：有效预测占总预测的比例
- **平均持续时间**：预测到结果的平均时间

### 系统性能
- **预测频率**：每小时的预测次数
- **响应时间**：数据更新到预测的延迟
- **稳定性**：系统连续运行时间

## 🎯 使用场景

### 场景1：模型开发和验证
```bash
# 1. 获取数据
python get_btc_history.py --limit 20000

# 2. 训练模型
python LightGBM_percentage_v2.py --mode train --save-data

# 3. 快速验证
python LightGBM_percentage_v2.py --mode validate --load-data

# 4. 回测验证
python backtest_percentage_v4.py btcusd_1-min_data.csv --max-wait-hours 4
```

### 场景2：实时交易监控
```bash
# 1. 确保有训练好的模型
ls btc_percentage_model.joblib model_config_percentage.json

# 2. 启动实时预测
python real_time_predictor.py --max-wait-hours 4 --update-interval 60

# 3. 监控日志
tail -f prediction_log.txt
```

### 场景3：策略研究
```bash
# 不同等待时间的效果对比
python backtest_percentage_v4.py btcusd_1-min_data.csv --max-wait-hours 1 > result_1h.txt
python backtest_percentage_v4.py btcusd_1-min_data.csv --max-wait-hours 4 > result_4h.txt
python backtest_percentage_v4.py btcusd_1-min_data.csv --max-wait-hours 8 > result_8h.txt

# 分析结果
grep "成功率" result_*.txt
```

### 场景4：生产部署
```bash
# 服务器环境运行
nohup python real_time_predictor.py \
    --max-wait-hours 4 \
    --update-interval 60 \
    --no-sound \
    --log-file /var/log/btc_predictions.log \
    > /var/log/btc_system.log 2>&1 &

# 监控脚本
watch -n 60 "tail -5 /var/log/btc_predictions.log"
```

## 🔄 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据获取层    │    │   模型训练层    │    │   预测应用层    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ get_btc_history │    │ LightGBM_v2     │    │ real_time_pred  │
│ binance_fetcher │───▶│ 特征工程        │───▶│ 实时预测        │
│ binance_websock │    │ 模型训练        │    │ 信号生成        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   验证测试层    │    │   监控日志层    │
                       ├─────────────────┤    ├─────────────────┤
                       │ backtest_v4     │    │ prediction_log  │
                       │ backtest_v2     │    │ 报警提醒        │
                       │ 策略验证        │    │ 性能监控        │
                       └─────────────────┘    └─────────────────┘
```

## 📈 技术特性

### 机器学习
- **算法**：LightGBM梯度提升
- **特征**：20个优化后的技术指标
- **目标**：百分比方向预测（先涨1% vs 先跌1%）
- **验证**：时间序列交叉验证

### 数据处理
- **数据源**：Binance API
- **频率**：1分钟K线数据
- **特征工程**：价格、成交量、技术指标、时间特征
- **缓存机制**：预处理数据缓存，提升验证速度

### 实时系统
- **更新频率**：可配置（30-120秒）
- **并发预测**：支持多个同时进行的预测
- **容错机制**：网络异常自动重试
- **报警系统**：多平台声音提醒

## 🛠️ 开发指南

### 添加新特征
1. 在`LightGBM_percentage_v2.py`的`calculate_features`函数中添加
2. 重新训练模型
3. 更新`real_time_predictor.py`中的特征计算

### 自定义预测目标
1. 修改`prepare_features_and_labels`函数
2. 调整`check_percentage_result`函数
3. 更新回测脚本的验证逻辑

### 扩展数据源
1. 参考`binance_btc_fetcher.py`的API调用方式
2. 实现新的数据获取脚本
3. 确保输出格式与现有系统兼容

## 📊 性能基准

### 典型性能指标
- **成功率**：60-75%（基于历史回测）
- **有效率**：70-85%（非超时预测比例）
- **平均持续时间**：30-90分钟
- **预测频率**：每小时1-3次

### 系统资源
- **内存使用**：~200MB（主要是1000条数据缓存）
- **CPU使用**：低（主要在预测时）
- **网络带宽**：极低（每分钟一次API调用）
- **磁盘空间**：日志文件随时间增长

## 🔐 安全考虑

### API安全
- 使用只读API（无需API密钥）
- 实现请求频率限制
- 网络异常处理

### 数据安全
- 本地存储，无敏感信息
- 定期备份重要文件
- 日志文件权限控制

### 系统安全
- 无特权要求
- 可在沙箱环境运行
- 错误隔离机制

---

## 📞 支持与贡献

### 文档
- **项目概述**：`project.md`
- **使用指南**：本README
- **API文档**：代码注释

### 日志和调试
- **预测日志**：`prediction_log.txt`
- **系统输出**：控制台显示
- **错误追踪**：异常信息记录

### 社区
- 欢迎提交Issue和Pull Request
- 分享使用经验和优化建议
- 讨论新功能和改进方案

**Happy Trading! 🚀📈**
