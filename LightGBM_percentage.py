import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import lightgbm as lgb
from sklearn.calibration import CalibratedClassifierCV
import joblib
import json
import argparse
import os

def main():
    """主函数，支持训练模式和验证模式"""
    parser = argparse.ArgumentParser(description="LightGBM模型 - 预测先涨1%还是先跌1%")
    parser.add_argument("--mode", choices=['train', 'validate'], default='train',
                       help="运行模式：train=训练新模型，validate=使用已保存模型验证")
    parser.add_argument("--model-file", default='btc_percentage_model.joblib',
                       help="模型文件路径")
    parser.add_argument("--config-file", default='model_config_percentage.json',
                       help="配置文件路径")

    args = parser.parse_args()

    if args.mode == 'train':
        print("开始训练LightGBM模型 - 预测先涨1%还是先跌1%")
        train_model()
    else:
        print("使用已保存模型验证测试集 - 预测先涨1%还是先跌1%")
        validate_model(args.model_file, args.config_file)

def train_model():
    """训练模式：完整的模型训练流程"""

    # ===================================================================
    #      第1部分: 数据加载和预处理
    # ===================================================================

    # 加载数据
    df = pd.read_csv('btcusd_1-min_data.csv')

    # 将Unix时间戳转换为日期时间格式，并设为索引
    df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s')
    df.set_index('Timestamp', inplace=True)

    # 筛选数据范围
    three_years_ago = datetime.now() - timedelta(days=365*3)
    df_recent = df.loc[three_years_ago:]
    print(f"原始数据范围: {df.index.min()} 到 {df.index.max()}")
    print(f"筛选后数据范围: {df_recent.index.min()} 到 {df_recent.index.max()}")
    df = df_recent

# 重命名列
df.rename(columns={
    'Open': 'open',
    'High': 'high',
    'Low': 'low',
    'Close': 'close',
    'Volume': 'volume'
}, inplace=True)

# 检查数据是否有缺失，并按时间排序
df.dropna(inplace=True)
df.sort_index(inplace=True)

print(f"数据预处理完成，共有 {len(df)} 条记录")

# ===================================================================
#      第2部分: 创建新的目标标签 - 先涨1%还是先跌1%
# ===================================================================

def create_percentage_target(df, up_threshold=0.01, down_threshold=0.01, max_lookforward=120):
    """
    创建新的目标标签：判断接下来的时间中是先涨1%还是先跌1%

    参数:
    - up_threshold: 上涨阈值（默认1%）
    - down_threshold: 下跌阈值（默认1%）
    - max_lookforward: 最大前瞻时间（分钟，默认120分钟）

    返回:
    - 1: 先涨1%
    - 0: 先跌1%
    - NaN: 在max_lookforward时间内都没有达到阈值
    """
    print(f"创建目标标签：先涨{up_threshold*100:.1f}%还是先跌{down_threshold*100:.1f}%")
    print(f"最大前瞻时间：{max_lookforward}分钟")

    labels = []
    valid_indices = []

    for i in range(len(df)):
        if i % 10000 == 0:
            print(f"处理进度: {i}/{len(df)} ({i/len(df)*100:.1f}%)")

        current_price = df.iloc[i]['close']
        up_target = current_price * (1 + up_threshold)
        down_target = current_price * (1 - down_threshold)

        # 查看接下来的价格走势
        label = None
        for j in range(1, min(max_lookforward + 1, len(df) - i)):
            future_price = df.iloc[i + j]['close']

            # 检查是否先达到上涨目标
            if future_price >= up_target:
                label = 1  # 先涨1%
                break
            # 检查是否先达到下跌目标
            elif future_price <= down_target:
                label = 0  # 先跌1%
                break

        if label is not None:
            labels.append(label)
            valid_indices.append(i)

    print(f"有效标签数量: {len(labels)}/{len(df)} ({len(labels)/len(df)*100:.1f}%)")

    # 创建标签Series
    label_series = pd.Series(index=df.index[valid_indices], data=labels)

    # 统计标签分布
    up_count = sum(labels)
    down_count = len(labels) - up_count
    print(f"标签分布: 先涨1% = {up_count} ({up_count/len(labels)*100:.1f}%), 先跌1% = {down_count} ({down_count/len(labels)*100:.1f}%)")

    return label_series

# 创建新的目标标签
target_labels = create_percentage_target(df, up_threshold=0.01, down_threshold=0.01, max_lookforward=120)

# 只保留有有效标签的数据
df_labeled = df.loc[target_labels.index].copy()
df_labeled['label'] = target_labels

print(f"最终数据集大小: {len(df_labeled)} 条记录")

# ===================================================================
#      第3部分: 特征工程
# ===================================================================

def calculate_features(df):
    """计算技术指标特征（基于重要性分析优化）"""
    print("开始计算特征（仅保留重要特征）...")

    epsilon = 1e-9

    # 1. 时间特征（最重要）
    df['hour'] = df.index.hour  # 重要性: 290
    df['day_of_week'] = df.index.dayofweek  # 重要性: 191

    # 2. ATR特征（第3重要）
    df = get_standardized_kline_features(df, timeframe_suffix='_1m', epsilon=epsilon)

    # 3. 价格/动量特征（保留重要的）
    df['return_60min'] = df['close'].pct_change(60)  # 重要性: 134
    df['return_30min'] = df['close'].pct_change(30)  # 重要性: 41
    df['return_10min'] = df['close'].pct_change(10)  # 重要性: 13
    df['return_5min'] = df['close'].pct_change(5)   # 重要性: 11
    df['return_3min'] = df['close'].pct_change(3)   # 重要性: 3
    # 去掉return_1min（重要性太低）

    # 4. 波动率特征（保留重要的）
    for n in [10, 30, 60]:
        df[f'volatility_{n}'] = df['close'].rolling(window=n).std()
        df[f'volatility_ratio_{n}'] = df[f'volatility_{n}'] / (df['close'] + epsilon)
    # volatility_ratio_60: 120, volatility_ratio_30: 83, volatility_ratio_10: 13

    # 5. 趋势特征（保留重要的）
    for n in [10, 30, 60]:
        df[f'sma_{n}'] = df['close'].rolling(window=n).mean()
        df[f'price_div_sma_{n}'] = df['close'] / (df[f'sma_{n}'] + epsilon)
    df['sma_10_div_sma_30'] = df['sma_10'] / (df['sma_30'] + epsilon)  # 重要性: 14
    # price_div_sma_60: 63, price_div_sma_30: 33, price_div_sma_10: 3

    # 6. VWAP特征（保留重要的）
    df['price_x_volume'] = df['close'] * df['volume']
    vwap_numerator = df['price_x_volume'].rolling(window=30).sum()
    vwap_denominator = df['volume'].rolling(window=30).sum()
    df['vwap_30'] = vwap_numerator / (vwap_denominator + epsilon)
    df['price_div_vwap_30'] = df['close'] / (df['vwap_30'] + epsilon)  # 重要性: 46
    df.drop('price_x_volume', axis=1, inplace=True)

    # 7. 成交量特征（只保留最重要的）
    df['vma_60'] = df['volume'].rolling(window=60).mean()
    df['volume_div_vma_60'] = df['volume'] / (df['vma_60'] + epsilon)  # 重要性: 5
    # 去掉vma_10和vma_30（重要性太低）

    # 8. 滚动K线特征（保留重要的）
    for window in [3, 5]:
        if f'range_norm_by_atr_1m' in df.columns:
            df[f'range_norm_by_atr_mean_{window}m'] = df['range_norm_by_atr_1m'].rolling(window=window).mean()
        # range_norm_by_atr_mean_3m: 14, range_norm_by_atr_mean_5m: 10

    # 只保留body_percent_mean_5m（重要性: 3）
    if f'body_percent_of_range_1m' in df.columns:
        df[f'body_percent_mean_5m'] = df['body_percent_of_range_1m'].rolling(window=5).mean()
    # 去掉body_percent_mean_3m（重要性太低）

    print("特征计算完成（已优化）")
    return df

def get_standardized_kline_features(df, timeframe_suffix='', epsilon=1e-9):
    """计算标准化的K线特征"""
    try:
        # 1. 计算ATR
        high_low = df['high'] - df['low']
        high_prev_close = abs(df['high'] - df['close'].shift(1))
        low_prev_close = abs(df['low'] - df['close'].shift(1))
        tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)
        atr_col = f'atr_14{timeframe_suffix}'
        df[atr_col] = tr.rolling(window=14).mean()

        # 2. 计算形态的绝对值
        body_size = abs(df['close'] - df['open'])
        price_range = df['high'] - df['low']
        upper_shadow = df['high'] - df[['open', 'close']].max(axis=1)
        lower_shadow = df[['open', 'close']].min(axis=1) - df['low']

        # 3. 标准化
        df[f'body_percent_of_range{timeframe_suffix}'] = body_size / (price_range + epsilon)
        df[f'upper_shadow_percent_of_range{timeframe_suffix}'] = upper_shadow / (price_range + epsilon)
        df[f'lower_shadow_percent_of_range{timeframe_suffix}'] = lower_shadow / (price_range + epsilon)
        df[f'range_norm_by_atr{timeframe_suffix}'] = price_range / (df[atr_col] + epsilon)

    except Exception as e:
        print(f"K线特征计算出错: {e}")

    return df

# 计算特征
df_with_features = calculate_features(df_labeled)

# 清理NaN值
df_clean = df_with_features.dropna()
print(f"清理NaN后剩余 {len(df_clean)} 条记录")

# ===================================================================
#      第4部分: 数据分割和模型训练
# ===================================================================

# 时间序列分割
train_size = int(len(df_clean) * 0.70)
val_size = int(len(df_clean) * 0.15)

train_df = df_clean.iloc[:train_size]
val_df = df_clean.iloc[train_size:train_size + val_size]
test_df = df_clean.iloc[train_size + val_size:]

print(f"训练集: {len(train_df)} 条记录 ({train_df.index.min()} 到 {train_df.index.max()})")
print(f"验证集: {len(val_df)} 条记录 ({val_df.index.min()} 到 {val_df.index.max()})")
print(f"测试集: {len(test_df)} 条记录 ({test_df.index.min()} 到 {test_df.index.max()})")

# 定义特征和目标（基于重要性分析优化）
target = 'label'

# 只排除基础数据列和中间计算列
excluded_columns = ['open', 'high', 'low', 'close', 'volume', 'label']
excluded_columns += [f'sma_{n}' for n in [10, 30, 60]]  # 中间计算列
excluded_columns += [f'volatility_{n}' for n in [10, 30, 60]]  # 中间计算列
excluded_columns += ['vwap_30', 'vma_60']  # 中间计算列

# 手动指定重要特征列表（基于特征重要性分析）
important_features = [
    # 时间特征（最重要）
    'hour',                    # 重要性: 290
    'day_of_week',            # 重要性: 191

    # ATR特征
    'atr_14_1m',              # 重要性: 169

    # 价格/动量特征
    'return_60min',           # 重要性: 134
    'return_30min',           # 重要性: 41
    'return_10min',           # 重要性: 13
    'return_5min',            # 重要性: 11
    'return_3min',            # 重要性: 3

    # 波动率特征
    'volatility_ratio_60',    # 重要性: 120
    'volatility_ratio_30',    # 重要性: 83
    'volatility_ratio_10',    # 重要性: 13

    # 趋势特征
    'price_div_sma_60',       # 重要性: 63
    'price_div_vwap_30',      # 重要性: 46
    'price_div_sma_30',       # 重要性: 33
    'sma_10_div_sma_30',      # 重要性: 14
    'price_div_sma_10',       # 重要性: 3

    # K线特征
    'range_norm_by_atr_mean_3m',  # 重要性: 14
    'range_norm_by_atr_mean_5m',  # 重要性: 10
    'body_percent_mean_5m',       # 重要性: 3

    # 成交量特征
    'volume_div_vma_60',      # 重要性: 5
]

# 检查哪些重要特征实际存在于数据中
available_features = [col for col in important_features if col in df_clean.columns]
missing_features = [col for col in important_features if col not in df_clean.columns]

if missing_features:
    print(f"警告：以下重要特征在数据中不存在: {missing_features}")

features = available_features
print(f"使用的重要特征数量: {len(features)}")
print(f"特征列表: {features}")

# 准备训练数据
X_train, y_train = train_df[features], train_df[target]
X_val, y_val = val_df[features], val_df[target]
X_test, y_test = test_df[features], test_df[target]

print(f"训练集大小: {len(X_train)}, 验证集大小: {len(X_val)}, 测试集大小: {len(X_test)}")

# 训练模型
print("开始训练LightGBM模型...")

lgbm = lgb.LGBMClassifier(
    objective='binary',
    metric='auc',
    n_estimators=2000,
    learning_rate=0.05,
    n_jobs=-1,
    random_state=42,
    verbose=-1
)

lgbm.fit(
    X_train, y_train,
    eval_set=[(X_val, y_val)],
    eval_metric='auc',
    callbacks=[lgb.early_stopping(stopping_rounds=100, verbose=False)]
)

print("基础模型训练完成")

# 概率校准
print("开始概率校准...")
calibrated_model = CalibratedClassifierCV(lgbm, method='isotonic', cv='prefit')
calibrated_model.fit(X_val, y_val)
print("概率校准完成")

# ===================================================================
#      第5部分: 阈值优化（新的评分规则）
# ===================================================================

print("开始阈值优化（新评分规则：成功+1，失败-1）...")

val_probabilities = calibrated_model.predict_proba(X_val)[:, 1]
thresholds_to_test = np.arange(0.52, 0.75, 0.01)

best_score = -np.inf
best_threshold = 0.5

for threshold in thresholds_to_test:
    current_score = 0
    trades_made = 0

    for i in range(len(val_probabilities)):
        prob = val_probabilities[i]
        actual_result = y_val.iloc[i]

        guess = None
        if prob > threshold:
            guess = 1  # 猜先涨1%
            trades_made += 1
        elif prob < (1 - threshold):
            guess = 0  # 猜先跌1%
            trades_made += 1

        if guess is not None:
            if guess == actual_result:
                current_score += 1  # 成功+1
            else:
                current_score -= 1  # 失败-1

    if current_score > best_score:
        best_score = current_score
        best_threshold = threshold

print(f"验证集上的最优得分: {best_score:.0f}")
print(f"找到的最优信心阈值: {best_threshold:.3f}")

# ===================================================================
#      第6部分: 测试集评估
# ===================================================================

print("\n开始测试集评估...")

test_probabilities = calibrated_model.predict_proba(X_test)[:, 1]
test_score = 0
trades_made = 0
wins = 0
losses = 0

print("\n" + "="*80)
print(f"--- 测试集详细评估 (使用阈值: {best_threshold:.3f}) ---")
print("-" * 80)
print(f"{'时间':<22}{'操作':<12}{'信心':<8}{'当前价格':<12}{'结果':<12}{'得分'}")
print("-" * 80)

for i in range(len(test_probabilities)):
    prob = test_probabilities[i]
    actual_result = y_test.iloc[i]
    current_price = test_df.iloc[i]['close']
    timestamp = test_df.index[i]

    guess = None
    action_str = "放弃"
    result_str = "-"
    score_for_this_trade = 0

    if prob > best_threshold:
        guess = 1
        action_str = "\033[92m猜先涨1%\033[0m"
        trades_made += 1
    elif prob < (1 - best_threshold):
        guess = 0
        action_str = "\033[91m猜先跌1%\033[0m"
        trades_made += 1

    if guess is not None:
        if guess == actual_result:
            wins += 1
            test_score += 1
            score_for_this_trade = 1
            result_str = "成功✅"
        else:
            losses += 1
            test_score -= 1
            score_for_this_trade = -1
            result_str = "失败❌"

    # 只显示前20个结果，避免输出过长
    if i < 20:
        print(f"{str(timestamp):<22}"
              f"{action_str:<20}"
              f"{prob:<8.2%}"
              f"{current_price:<12.2f}"
              f"{result_str:<12}"
              f"{score_for_this_trade:+d}")

print("-" * 80)
print("\n--- 测试集最终评估结果 ---")
print(f"使用的阈值: {best_threshold:.3f}")
print(f"总样本数: {len(test_df)}")
print(f"猜测次数: {trades_made} (占比: {trades_made/len(test_df)*100:.2f}%)")
if trades_made > 0:
    print(f"胜率: {wins/trades_made*100:.2f}%")
print(f"总得分: {test_score:+d}")

# ===================================================================
#      第7部分: 保存模型和配置
# ===================================================================

print("\n保存模型和配置...")

joblib.dump(calibrated_model, 'btc_percentage_model.joblib')

config = {
    'best_threshold': best_threshold,
    'feature_list': features,
    'model_type': 'LightGBM_percentage_target',
    'target_description': 'predict_first_1percent_move',
    'scoring_rule': 'success_+1_failure_-1',
    'training_date': datetime.now().isoformat(),
    'train_size': len(X_train),
    'val_size': len(X_val),
    'test_size': len(X_test),
    'up_threshold': 0.01,
    'down_threshold': 0.01,
    'max_lookforward_minutes': 120
}

with open('model_config_percentage.json', 'w') as f:
    json.dump(config, f, indent=2)

print("模型和配置保存完成")
print(f"模型文件: btc_percentage_model.joblib")
print(f"配置文件: model_config_percentage.json")

print("\n训练完成！新的目标：预测先涨1%还是先跌1%")
print("评分规则：成功+1分，失败-1分")

# ===================================================================
#      第8部分: 特征重要性分析
# ===================================================================

print("\n" + "="*60)
print("特征重要性分析")
print("="*60)

feature_importance = lgbm.feature_importances_
feature_names = features

# 创建特征重要性DataFrame并排序
importance_df = pd.DataFrame({
    'feature': feature_names,
    'importance': feature_importance
}).sort_values('importance', ascending=False)

print("\n前20个最重要的特征：")
print("-" * 40)
for i, (_, row) in enumerate(importance_df.head(20).iterrows()):
    print(f"{i+1:2d}. {row['feature']:<25} {row['importance']:>8.0f}")

print(f"\n特征重要性已保存到 feature_importance_percentage.csv")
importance_df.to_csv('feature_importance_percentage.csv', index=False)
