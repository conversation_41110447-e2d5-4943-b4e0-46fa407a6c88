[2025-07-12 16:57:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117801.84), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 16:57:07] 系统停止: 预测总结 | 数据: {'total_predictions': 0, 'successful': 0, 'failed': 0, 'timeout': 0, 'success_rate': 0.0}
[2025-07-12 16:58:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117838.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 16:59:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117890.32), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:00:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117944.53), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:01:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117918.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:02:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117896.6), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:03:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117896.6), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:03:52] 系统停止: 用户中断
[2025-07-12 17:03:53] 系统停止: 预测总结 | 数据: {'total_predictions': 0, 'successful': 0, 'failed': 0, 'timeout': 0, 'success_rate': 0.0}
[2025-07-12 17:04:03] 模型加载: 成功加载模型和配置
[2025-07-12 17:04:03] 数据获取: 初始数据获取成功，1000条记录
[2025-07-12 17:04:05] 新预测: pred_20250712_170400 - 先涨1% | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117881.15), 'up_target': np.float64(119059.96149999999), 'down_target': np.float64(116702.3385)}
[2025-07-12 17:04:14] 系统停止: 用户中断
[2025-07-12 17:04:20] 系统停止: 预测总结 | 数据: {'total_predictions': 1, 'successful': 0, 'failed': 0, 'timeout': 0, 'success_rate': 0.0}
[2025-07-12 17:04:23] 模型加载: 成功加载模型和配置
[2025-07-12 17:04:23] 数据获取: 初始数据获取成功，1000条记录
[2025-07-12 17:04:25] 新预测: pred_20250712_170400 - 先涨1% | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117881.16), 'up_target': np.float64(119059.9716), 'down_target': np.float64(116702.3484)}
[2025-07-12 17:05:12] 新预测: pred_20250712_170500 - 先涨1% | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117918.8), 'up_target': np.float64(119097.988), 'down_target': np.float64(116739.61200000001)}
[2025-07-12 17:06:54] 新预测: pred_20250712_170600 - 先涨1% | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117902.3), 'up_target': np.float64(119081.323), 'down_target': np.float64(116723.277)}
[2025-07-12 17:07:11] 新预测: pred_20250712_170700 - 先涨1% | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117890.8), 'up_target': np.float64(119069.708), 'down_target': np.float64(116711.892)}
[2025-07-12 17:07:19] 系统停止: 用户中断
[2025-07-12 17:07:26] 系统停止: 预测总结 | 数据: {'total_predictions': 4, 'successful': 0, 'failed': 0, 'timeout': 0, 'success_rate': 0.0}
[2025-07-12 17:07:34] 模型加载: 成功加载模型和配置
[2025-07-12 17:07:34] 数据获取: 初始数据获取成功，1000条记录
[2025-07-12 17:07:34] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117890.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:08:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117890.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:09:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117890.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:10:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117890.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:11:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117909.34), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:12:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117909.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:13:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117894.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:14:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117888.72), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:15:22] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 17:15:28] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117888.72), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:15:58] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118027.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:16:59] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118008.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:18:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117939.68), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:19:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117972.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:20:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117973.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:21:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117973.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:22:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117972.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:23:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118012.34), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:24:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118032.74), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:25:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118032.31), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:26:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118002.54), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:27:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117980.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:28:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117980.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:29:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117980.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:29:58] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117980.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:31:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117977.12), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:32:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117951.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:33:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117951.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:34:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117951.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:35:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117938.61), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:36:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117902.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:37:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117938.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:38:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117980.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:39:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118031.56), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:40:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117989.53), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:41:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118040.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:42:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118040.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:43:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118027.31), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:43:59] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118011.53), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:45:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118054.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:46:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118023.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:47:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118014.54), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:48:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118040.89), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:49:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118040.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:49:45] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 17:50:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118060.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:51:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118072.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:52:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118130.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:53:10] 新预测: pred_20250712_175300 - 先涨1% | 数据: {'probability': np.float64(0.9238095238095239), 'price': np.float64(118199.99), 'up_target': np.float64(119381.9899), 'down_target': np.float64(117017.99010000001)}
[2025-07-12 17:53:35] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 17:54:13] 新预测: pred_20250712_175400 - 先涨1% | 数据: {'probability': np.float64(0.9238095238095239), 'price': np.float64(118199.83), 'up_target': np.float64(119381.82830000001), 'down_target': np.float64(117017.8317)}
[2025-07-12 17:54:38] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 17:55:00] 新预测: pred_20250712_175500 - 先涨1% | 数据: {'probability': np.float64(0.9238095238095239), 'price': np.float64(118184.39), 'up_target': np.float64(119366.2339), 'down_target': np.float64(117002.54609999999)}
[2025-07-12 17:55:41] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 17:55:59] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118083.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:57:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118122.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:58:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118160.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:59:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118158.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:00:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118158.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:00:38] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:00:59] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118085.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:02:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118081.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:03:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118080.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:04:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118035.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:05:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118064.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:06:14] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 18:06:20] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118060.16), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:07:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118089.24), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:08:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118054.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:09:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118006.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:10:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118006.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:11:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118001.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:12:00] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:12:10] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:12:20] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:12:30] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:12:40] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:12:51] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:13:01] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:13:11] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:13:21] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:13:31] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:13:41] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:13:51] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:14:01] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:14:11] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:14:21] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:14:26] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117931.67), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:14:57] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117980.94), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:15:58] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117987.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:16:59] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(118000.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:18:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118050.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:18:40] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 18:19:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118033.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:20:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118086.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:21:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118093.84), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:22:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118085.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:23:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118076.77), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:24:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118063.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:25:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118040.12), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:26:03] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 18:26:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118078.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:27:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118051.16), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:28:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118075.2), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:28:56] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118065.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:29:57] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118065.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:30:59] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118073.2), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:32:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118100.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:33:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118129.13), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:33:26] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 18:34:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118140.96), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:34:57] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 18:35:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118122.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:36:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118076.77), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:37:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118061.61), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:38:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118104.81), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:39:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118100.42), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:40:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118100.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:40:43] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:40:53] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:41:03] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:41:13] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:41:23] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:41:33] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:41:43] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:41:53] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:41:59] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118100.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:43:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118100.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:44:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118100.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:45:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118100.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:46:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118088.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:47:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118100.43), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:48:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118105.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:49:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118084.47), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:50:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(118033.15), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:51:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(118023.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:52:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118064.31), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:53:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(118037.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:53:57] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(118048.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:54:58] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118068.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:55:59] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118068.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:57:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118068.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:58:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118089.13), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:59:16] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 18:59:21] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118089.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:00:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118077.32), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:01:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118083.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:02:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118083.05), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:03:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118106.2), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:04:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118057.64), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:05:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117995.43), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:06:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117968.08), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:07:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117968.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:08:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117945.93), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:09:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117961.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:10:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117966.34), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:10:44] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 19:10:59] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 19:11:14] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 19:11:19] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117976.1), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:12:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117940.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:13:16] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 19:13:22] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117930.37), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:14:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117944.33), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:15:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117944.33), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:16:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117936.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:17:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117933.67), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:18:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117943.58), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:19:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117948.24), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:20:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117959.57), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:20:55] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 19:21:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117975.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:22:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117975.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:23:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117999.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:24:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118020.93), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:25:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(118009.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:26:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(118009.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:27:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(118009.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:28:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118009.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:29:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118009.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:30:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118009.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:31:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118009.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:32:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117960.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:33:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117940.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:34:09] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 19:34:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117939.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:35:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117939.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:36:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117908.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:37:12] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 19:37:17] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117888.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:38:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117888.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:39:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117957.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:40:00] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 19:40:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117991.62), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:41:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118021.09), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:42:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117750.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:43:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117683.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:44:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117730.43), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:45:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117704.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:46:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117738.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:47:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117739.09), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:48:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117755.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:49:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117739.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:50:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117764.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:51:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117744.08), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:52:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117747.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:53:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117771.96), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:54:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117805.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:55:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117813.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:56:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117801.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:57:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117764.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:58:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117787.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:59:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117782.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:00:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117810.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:00:54] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:01:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117827.74), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:02:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117827.74), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:03:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117827.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:04:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117861.18), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:05:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117834.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:06:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117834.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:07:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117789.08), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:08:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117781.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:09:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117748.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:10:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117762.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:10:45] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 20:11:16] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:11:26] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 20:11:31] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117731.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:12:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117731.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:13:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117731.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:14:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117749.33), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:15:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117779.51), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:16:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117736.84), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:17:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117727.85), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:18:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117710.29), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:19:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117694.51), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:20:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117721.63), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:21:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117694.51), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:22:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117710.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:23:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117676.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:23:55] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:24:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117700.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:25:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117664.84), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:25:27] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:26:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117694.07), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:27:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117770.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:28:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117760.02), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:29:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117737.16), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:30:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117667.96), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:31:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117709.33), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:32:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117590.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:33:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117676.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:34:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117629.61), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:35:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117552.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:36:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117571.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:37:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117593.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:38:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117492.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:39:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117536.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:40:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117509.76), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:40:55] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 20:41:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117550.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:42:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117533.66), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:43:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117491.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:44:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117507.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:45:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117496.81), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:46:16] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:46:21] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117532.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:47:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117538.06), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:48:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117455.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:49:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117380.1), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:50:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117387.17), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:50:51] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:51:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117493.53), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:52:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117451.19), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:52:38] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:53:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117455.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:54:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117478.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:55:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117494.28), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:56:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117476.07), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:56:27] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:57:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117466.13), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:57:43] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:58:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117450.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:59:15] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:59:20] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117509.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:00:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117490.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:01:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117509.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:02:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117435.32), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:03:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117434.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:04:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117487.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:05:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117453.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:06:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117508.37), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:07:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117555.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:08:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117508.45), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:09:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117468.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:10:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117471.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:11:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117471.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:12:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117441.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:12:29] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 21:13:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117401.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:14:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117448.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:15:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117463.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:16:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117437.73), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:17:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117406.17), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:18:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117412.02), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:19:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117396.24), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:20:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117340.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:21:20] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 21:21:25] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117370.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:22:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117360.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:23:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117339.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:24:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117388.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:25:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117406.03), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:26:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117400.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:27:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117325.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:28:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117319.08), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:29:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117308.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:30:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117304.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:31:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117361.12), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:32:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117483.6), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:33:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117444.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:34:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117428.37), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:35:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117420.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:36:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117343.94), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:37:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117320.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:38:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117362.49), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:39:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117423.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:40:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117409.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:40:58] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 21:41:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117360.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:42:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117321.67), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:43:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117302.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:44:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117288.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:45:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117340.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:46:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117280.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:47:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117335.05), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:47:50] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 21:48:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117442.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:49:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117330.85), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:50:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117350.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:51:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117433.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:52:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117433.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:53:03] 预测完成: pred_20250712_175300 - 超时⏰ | 数据: {'direction': '先涨1%', 'probability': np.float64(0.9238095238095239), 'start_price': np.float64(118199.99), 'end_price': 117391.47, 'duration_minutes': 240, 'reason': '超时'}
[2025-07-12 21:53:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117391.47), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:54:05] 预测完成: pred_20250712_175400 - 超时⏰ | 数据: {'direction': '先涨1%', 'probability': np.float64(0.9238095238095239), 'start_price': np.float64(118199.83), 'end_price': 117430.22, 'duration_minutes': 240, 'reason': '超时'}
[2025-07-12 21:54:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117430.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:55:17] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 21:55:23] 预测完成: pred_20250712_175500 - 超时⏰ | 数据: {'direction': '先涨1%', 'probability': np.float64(0.9238095238095239), 'start_price': np.float64(118184.39), 'end_price': 117437.48, 'duration_minutes': 240, 'reason': '超时'}
[2025-07-12 21:55:23] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117437.48), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:56:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117481.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:57:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117468.57), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:58:07] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 21:58:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117463.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:59:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117457.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:00:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117509.76), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:01:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117480.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:01:53] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 22:02:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117470.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:03:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117453.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:04:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117433.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:05:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117405.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:06:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117437.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:07:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117494.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:08:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117456.93), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:09:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117509.76), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:10:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117547.76), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:11:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117726.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:12:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117681.1), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:13:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117666.69), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:14:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117583.37), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:15:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117602.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:15:55] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 22:16:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117601.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:17:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117588.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:18:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117658.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:18:28] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 22:19:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117620.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:19:29] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 22:20:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117654.14), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:20:56] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 22:21:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117560.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:22:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117681.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:22:43] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 22:23:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117723.14), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:24:15] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 22:24:20] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117699.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:25:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117596.63), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:26:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117596.63), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:27:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117596.63), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:28:04] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 22:28:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117601.07), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:29:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117601.07), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:30:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117620.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:31:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117620.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:32:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117568.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:33:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117567.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:34:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117563.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:35:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117553.07), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:36:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117475.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:37:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117490.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:38:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117470.26), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:39:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117434.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:40:12] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 22:40:22] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 22:40:32] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 22:40:38] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117419.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:41:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117460.54), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:42:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117466.48), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:43:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117466.48), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:43:51] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 22:44:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117466.47), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:45:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117466.47), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:45:39] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 22:46:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117462.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:47:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117450.61), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:48:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117450.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:49:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117430.72), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:50:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117422.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:51:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117413.63), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:52:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117400.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:53:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117389.47), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:54:20] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 22:54:26] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117329.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:55:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117329.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:56:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117360.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:57:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117360.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:58:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117360.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:59:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117376.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:00:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117384.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:01:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117378.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:02:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117378.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:03:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117364.89), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:04:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117364.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:05:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117359.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:06:22] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 23:06:27] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117370.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:07:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117350.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:08:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117315.87), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:09:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117315.87), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:10:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117349.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:11:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117387.18), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:12:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117322.87), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:13:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117328.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:14:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117364.43), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:15:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117433.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:16:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117400.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:17:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117415.31), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:18:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117382.42), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:19:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117417.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:20:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117417.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:21:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117382.6), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:22:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117377.6), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:23:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117360.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:24:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117325.81), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:25:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117309.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:26:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117170.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:27:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117175.49), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:28:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117277.66), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:29:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117347.77), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:30:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117379.49), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:31:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117408.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:32:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117318.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:33:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117248.06), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:34:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117243.49), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:35:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117265.64), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:36:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117217.58), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:37:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117120.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:38:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117140.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:39:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117122.69), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:40:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117173.62), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:41:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117055.88), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:42:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117039.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:43:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117000.68), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:44:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(116964.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:45:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(116976.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:46:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117057.46), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:47:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117018.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:48:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117013.13), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:49:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(116978.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:50:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117069.07), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:51:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117049.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:52:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117020.26), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:53:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117002.44), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:54:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(116940.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:55:14] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 23:55:20] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117050.2), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:56:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117029.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:57:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117070.68), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:58:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117116.29), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 23:59:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117117.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:00:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117100.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:01:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117087.67), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:02:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117057.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:03:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117150.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:04:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117145.72), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:05:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117164.88), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:06:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117104.16), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:07:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117141.05), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:08:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117151.53), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:09:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117171.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:10:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117252.43), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:11:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117206.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:12:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117217.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:13:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117266.66), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:14:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117266.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:14:56] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 00:15:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117266.66), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:16:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117266.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:16:44] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 00:17:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117257.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:18:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117279.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:19:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117320.29), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:20:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117320.28), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:21:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117332.17), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:22:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117358.46), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:23:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117304.05), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:24:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117299.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:25:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117299.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:26:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117364.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:27:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117364.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:28:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117329.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:29:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117278.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:30:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117295.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:31:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117284.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:32:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117299.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:33:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117338.71), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:34:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117342.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:35:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117329.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:36:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117329.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:37:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117329.84), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:38:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117329.84), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:39:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117329.84), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:40:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117338.88), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:41:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117364.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:42:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117364.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:43:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117376.42), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:44:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117256.84), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:45:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117279.54), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:46:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117319.62), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:47:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117333.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:48:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117320.43), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:49:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117320.43), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:50:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117293.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:51:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117229.61), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:52:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117185.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:53:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117189.23), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:54:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117178.46), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:55:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117178.47), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:56:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117179.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:57:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117178.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:57:41] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 00:58:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117196.57), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 00:59:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117196.58), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:00:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117191.09), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:01:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117213.31), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:02:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117194.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:03:18] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 01:03:24] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117187.77), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:04:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117187.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:05:06] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 01:05:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117224.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:06:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117256.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:07:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117274.56), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:08:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117295.06), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:09:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117291.68), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:10:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117291.68), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:11:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117329.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:12:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117329.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:13:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117306.2), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:14:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117314.15), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:15:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117310.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:16:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117340.71), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:17:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117335.31), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:18:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117371.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:18:54] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 01:19:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117371.54), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:20:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117371.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:21:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117371.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:22:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117398.66), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:23:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117377.77), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:24:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117377.77), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:25:18] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 01:25:23] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117402.02), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:26:20] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 01:26:25] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117433.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:27:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117438.18), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:28:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117396.73), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:29:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117445.56), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:30:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117445.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:31:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117394.53), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:32:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117441.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:33:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117436.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:34:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117413.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:35:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117440.18), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:36:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117440.18), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:37:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117410.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:38:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117424.15), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:39:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117424.15), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:40:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117495.86), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:41:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117536.96), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:42:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117510.71), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:43:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117510.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:44:04] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x1033383e0>: Failed to resolve 'api.binance.com' ([Errno 8] nodename nor servname provided, or not known)"))
[2025-07-13 01:44:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117455.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:45:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117461.69), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:46:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117422.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:47:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117444.2), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:48:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117489.89), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:49:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117458.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:50:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117444.89), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:51:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117444.88), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:52:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117414.67), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:53:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117414.68), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:54:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117414.67), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:55:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117414.68), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:56:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117414.67), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:57:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117414.68), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:58:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117467.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 01:59:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117501.07), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:00:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117467.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:01:11] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 02:01:16] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117455.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:02:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117449.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:03:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117475.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:04:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117474.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:05:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117461.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:05:31] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 02:06:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117449.51), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:07:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117498.26), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:08:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117498.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:09:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117474.77), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:10:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117448.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:11:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117466.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:12:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117466.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:13:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117465.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:13:26] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 02:14:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117465.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:14:27] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 02:15:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117465.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:16:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117465.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:17:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117473.61), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:18:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117444.74), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:19:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117444.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:20:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117444.74), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:21:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117444.74), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:22:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117422.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:23:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117407.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:24:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117396.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:25:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117428.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:26:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117416.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:27:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117416.64), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:28:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117416.64), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:29:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117416.64), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:30:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117416.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:31:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117433.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:32:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117430.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:33:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117444.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:34:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117433.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:34:33] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-13 02:34:43] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-13 02:35:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117433.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:36:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117433.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:37:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117433.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:38:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117433.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:39:20] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 02:39:25] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117433.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:40:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117394.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:41:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117394.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:42:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117394.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:43:10] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 02:43:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117378.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:44:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117378.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:45:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117378.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:45:28] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 02:46:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117402.48), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:47:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117391.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:48:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117380.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:49:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117403.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:50:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117415.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:51:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117409.64), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:52:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117380.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:53:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117380.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:54:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117380.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:55:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117396.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:55:26] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 02:56:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117432.25), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:57:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117432.26), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:58:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117432.26), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 02:59:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117509.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:00:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117504.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:00:34] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 03:01:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117504.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:02:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117483.94), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:02:36] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 03:03:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117525.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:04:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117547.54), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:05:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117543.2), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:06:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117543.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:07:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117557.93), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:07:58] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 03:08:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117600.81), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:09:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117598.14), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:10:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117598.14), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:11:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117586.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:12:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117608.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:13:20] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 03:13:26] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117608.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:14:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117608.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:15:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117608.53), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:16:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117608.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:17:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117608.53), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:18:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117620.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:19:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117639.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:20:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117639.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:21:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117639.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:22:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117639.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:23:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117639.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:24:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117582.54), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:25:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117534.6), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:26:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117534.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:27:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117481.87), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:28:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117486.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:29:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117484.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:30:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117439.72), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:31:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117439.72), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:32:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117452.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:33:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117450.32), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:34:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117450.32), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:35:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117479.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:36:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117553.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:37:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117525.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:38:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117516.93), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:39:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117516.93), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:40:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117547.62), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:41:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117547.62), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:42:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4), 'price': np.float64(117567.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:43:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4), 'price': np.float64(117567.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:44:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117567.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:45:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117595.13), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:46:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117609.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:47:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117620.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:48:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117620.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:49:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117596.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:50:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117580.45), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:51:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117580.12), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:52:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117560.69), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:53:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117560.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:54:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117560.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:54:30] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 03:55:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117560.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:56:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117560.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:57:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117527.03), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:58:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117550.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 03:59:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117550.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:00:23] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 04:00:28] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117515.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:01:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117546.24), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:02:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117546.25), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:03:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117546.24), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:04:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117546.25), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:04:43] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 04:05:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117520.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:06:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117508.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:07:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117478.54), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:08:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117510.03), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:09:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117510.02), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:10:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117498.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:11:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117498.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:12:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117483.69), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:13:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117464.44), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:14:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117464.45), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:15:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117451.43), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:16:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117451.44), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:17:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117451.43), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:18:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117447.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:19:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117447.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:20:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117429.2), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:21:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117419.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:22:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117419.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:23:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117428.87), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:24:09] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 04:24:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117439.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:24:39] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 04:25:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117452.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:26:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117442.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:27:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117442.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:28:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117442.96), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:29:01] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 04:29:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117442.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:30:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117442.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:31:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117417.44), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:32:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117365.46), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:33:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117370.57), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:34:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117370.57), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:35:24] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 04:35:29] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117370.56), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:36:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117370.56), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:36:40] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 04:37:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117370.56), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:38:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117370.57), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:39:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117370.56), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:40:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117356.58), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:41:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117356.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:42:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117356.58), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:43:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117346.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:44:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117346.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:45:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117346.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:46:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117346.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:47:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117346.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:48:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117346.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:49:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117346.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:50:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117346.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:51:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117346.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:52:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117335.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:53:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117250.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:54:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117204.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:55:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117153.18), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:55:35] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 04:56:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117124.42), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:57:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117129.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:58:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117084.73), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 04:59:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117084.73), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:00:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117023.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:01:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117011.72), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:02:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117149.45), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:03:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117158.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:04:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117226.45), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:05:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117188.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:06:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117259.43), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:07:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117250.68), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:08:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117250.68), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:09:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117272.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:10:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117260.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:11:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117260.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:12:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117250.69), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:13:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117250.68), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:14:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117209.29), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:15:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117146.57), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:16:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117159.72), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:17:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117168.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:18:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117224.24), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:19:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117272.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:20:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117221.77), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:21:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117248.87), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:22:11] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 05:22:16] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117260.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:23:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117260.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:24:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117260.6), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:25:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117260.6), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:26:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117287.57), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:27:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117291.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:28:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117304.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:29:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117336.03), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:30:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117315.86), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:31:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117315.86), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:32:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117290.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:33:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117331.76), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:34:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117346.33), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:35:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117346.33), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:36:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117346.34), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:37:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117403.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:38:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117470.08), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:39:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117411.66), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:40:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117453.07), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:41:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117453.08), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:42:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117454.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:43:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117495.42), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:44:10] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 05:44:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117553.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:45:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117555.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:46:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117553.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:47:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117478.88), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:48:15] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 05:48:21] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117439.72), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:49:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117436.16), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:50:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117415.14), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:51:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117447.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:52:05] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 05:52:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117447.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:53:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117450.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:54:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117450.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:55:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117450.86), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:56:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117450.87), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:57:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117452.69), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:58:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117458.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 05:59:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117461.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:00:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117505.71), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:01:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117506.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:02:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117502.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:03:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117509.76), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:04:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117506.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:05:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117499.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:06:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117446.03), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:07:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117437.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:08:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117447.44), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:09:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117457.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:10:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117462.31), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:11:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117461.69), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:12:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117433.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:13:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117448.31), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:14:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117448.31), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:15:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117460.05), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:16:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117422.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:16:53] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 06:17:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117410.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:18:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117376.49), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:19:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117312.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:20:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117339.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:21:14] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 06:21:19] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117368.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:22:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117376.49), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:23:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117406.26), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:24:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117406.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:25:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117410.74), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:26:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117420.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:27:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117440.93), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:27:53] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 06:28:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117440.94), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:29:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117456.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:30:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117456.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:31:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117456.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:32:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117456.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:33:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117491.25), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:34:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117491.25), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:35:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117469.25), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:36:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117469.12), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:37:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117469.12), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:38:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117440.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:39:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117416.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:40:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117416.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:41:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117416.81), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:42:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117416.81), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:43:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117432.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:44:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117388.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:45:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117404.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:46:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117385.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:47:04] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 06:47:19] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 06:47:24] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117379.05), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:48:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117379.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:49:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117344.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:50:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117348.61), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:51:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117357.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:52:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117364.54), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:53:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117370.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:54:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117370.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:55:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117385.37), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:56:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117385.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:57:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117385.37), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:58:04] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 06:58:19] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 06:58:24] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117385.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 06:59:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117385.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:00:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117385.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:01:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117411.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:02:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117411.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:02:39] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 07:03:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117408.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:04:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117400.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:05:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117365.13), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:06:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117345.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:07:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117345.56), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:08:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117320.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:09:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117288.19), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:09:33] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 07:10:19] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 07:10:24] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117284.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:11:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117284.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:12:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117280.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:13:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117276.53), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:14:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117232.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:15:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117252.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:16:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117277.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:17:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117291.68), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:18:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117336.74), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:19:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117306.47), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:20:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117286.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:21:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117286.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:22:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117286.12), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:23:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117321.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:24:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117321.05), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:25:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117287.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:26:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117279.37), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:27:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117279.37), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:27:42] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 07:28:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117287.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:29:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117287.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:30:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117288.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:31:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117307.67), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:32:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117307.68), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:33:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117317.2), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:34:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117312.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:35:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117312.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:36:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117312.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:37:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117312.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:38:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117312.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:39:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117299.24), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:39:57] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 07:40:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117291.67), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:41:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117291.67), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:42:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117280.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:43:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117276.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:44:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117261.31), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:45:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117258.96), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:46:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117258.96), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:46:51] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 07:47:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117335.29), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:48:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117335.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:49:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117347.03), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:50:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117439.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:51:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117460.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:52:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117440.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:53:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117443.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:54:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117405.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:55:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117399.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:56:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117399.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:57:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117384.66), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:58:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117378.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 07:59:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117400.94), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:00:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117439.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:01:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117448.56), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:02:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117429.14), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:03:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117407.84), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:04:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117385.31), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:05:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117362.57), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:06:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117360.72), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:07:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117335.58), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:08:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117329.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:09:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117300.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:10:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117300.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:11:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117300.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:12:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117302.18), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:13:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117300.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:14:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117308.48), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:15:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117320.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:16:14] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 08:16:29] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 08:16:34] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117260.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:17:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117256.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:18:01] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 08:18:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117250.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:19:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117258.62), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:19:48] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 08:20:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117256.02), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:21:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117341.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:22:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117359.08), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:23:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117383.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:24:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117398.61), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:25:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117365.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:26:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117398.56), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:27:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117381.06), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:27:44] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 08:28:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117370.72), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:29:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117370.72), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:30:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117392.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:31:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117378.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:32:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117418.47), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:33:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117391.89), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:34:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117369.76), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:35:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117359.48), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:36:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117359.48), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:37:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117298.76), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:38:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117264.15), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:39:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117312.09), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:40:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117355.44), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:41:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117366.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:42:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117408.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:43:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117420.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:44:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117420.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:45:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117411.69), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:46:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117355.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:47:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117405.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:48:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117378.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:49:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117373.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:50:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117348.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:51:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117348.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:52:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117361.47), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:53:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117361.47), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:54:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117335.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:55:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117330.54), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:56:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117330.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:57:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117319.44), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:58:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117319.45), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 08:59:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117316.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:00:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117316.1), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:01:13] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 09:01:18] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117316.1), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:02:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117300.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:03:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117376.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:04:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117390.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:04:32] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 09:05:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117390.77), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:06:20] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 09:06:25] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117411.74), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:07:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117390.71), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:08:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117390.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:09:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117364.03), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:10:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117340.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:11:11] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 09:11:16] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117333.17), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:12:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117375.77), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:12:58] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 09:13:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117375.77), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:14:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117375.76), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:15:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117339.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:16:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117356.32), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:17:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117335.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:18:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117348.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:19:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117306.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:20:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117317.66), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:21:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117334.45), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:22:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117378.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:23:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117378.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:24:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117398.26), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:25:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117357.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:25:45] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 09:26:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117381.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:27:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117404.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:28:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117377.43), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:29:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117387.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:30:20] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 09:30:26] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117398.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:31:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117383.71), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:32:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117406.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:33:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117406.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:34:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117406.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:35:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117380.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:35:58] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 09:36:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117393.24), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:36:43] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 09:37:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117440.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:38:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117454.09), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:39:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117514.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:40:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117501.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:41:20] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 09:41:25] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117491.89), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:42:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117496.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:43:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117509.76), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:44:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117509.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:45:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117536.96), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:46:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117533.31), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:47:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117553.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:48:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117595.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:49:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117600.86), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:50:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117602.96), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:51:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117617.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:52:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117623.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:53:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117640.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:54:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117632.07), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:55:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117600.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:56:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117553.37), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:57:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117543.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:58:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117570.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:59:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117563.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 09:59:30] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 10:00:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117553.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:01:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117524.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:02:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117504.76), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:02:44] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-13 10:02:54] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-13 10:03:04] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-13 10:03:14] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-13 10:03:19] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117497.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:04:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117497.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:05:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117504.53), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:06:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117485.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:07:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117487.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:07:35] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 10:08:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117494.29), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:09:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117488.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:10:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.6113744075829384), 'price': np.float64(117514.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:11:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.6113744075829384), 'price': np.float64(117514.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:12:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117493.09), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:13:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.6113744075829384), 'price': np.float64(117484.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:14:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.6113744075829384), 'price': np.float64(117484.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:15:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117483.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:16:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117466.15), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:17:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117460.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:18:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117455.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:19:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117418.74), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:20:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117427.94), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:21:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117427.93), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:22:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117420.29), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:23:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117420.29), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:24:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117420.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:24:42] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 10:25:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117420.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:26:14] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 10:26:20] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117420.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:27:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117420.29), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:28:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117429.81), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:29:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117429.81), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:30:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117429.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:31:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117415.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:32:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117413.16), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:33:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117422.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:34:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117439.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:34:25] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 10:35:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117439.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:36:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117407.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:37:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117441.42), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:38:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117441.42), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:39:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117441.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:40:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117431.19), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:41:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117431.19), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:42:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117479.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:43:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117500.53), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:44:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117505.61), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:45:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117505.61), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:46:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117517.62), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:47:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117553.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:48:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117553.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:49:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117553.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:50:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117553.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:51:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117553.37), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:52:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117553.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:53:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117581.31), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:53:36] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 10:54:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117627.33), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:55:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117646.86), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:55:38] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 10:56:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117690.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:57:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117675.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:58:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117660.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 10:58:27] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 10:59:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117628.76), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:00:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117660.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:01:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117660.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:01:31] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 11:02:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117660.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:03:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117626.25), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:04:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117638.49), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:05:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117656.12), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:05:51] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 11:06:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117684.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:06:37] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 11:07:23] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 11:07:28] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117620.43), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:08:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117626.53), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:09:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117626.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:10:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117626.53), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:11:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117600.81), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:12:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117600.81), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:13:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117600.81), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:14:16] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 11:14:22] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117600.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:15:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117600.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:16:19] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 11:16:24] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.6113744075829384), 'price': np.float64(117653.23), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:17:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.6113744075829384), 'price': np.float64(117653.23), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:18:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.6113744075829384), 'price': np.float64(117653.23), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:19:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117682.06), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:20:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117695.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:20:39] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 11:21:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117747.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:22:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117747.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:23:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117753.61), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:24:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117753.61), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:25:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.6113744075829384), 'price': np.float64(117747.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:26:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117765.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:27:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117765.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:28:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.6113744075829384), 'price': np.float64(117765.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:29:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117765.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:29:51] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 11:30:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117765.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:31:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117765.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:32:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117791.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:32:40] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 11:33:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117791.84), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:34:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117791.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:35:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117799.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:36:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117800.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:37:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117824.76), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:38:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117775.68), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:39:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117792.07), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:40:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117750.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:41:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117792.07), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:42:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117800.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:43:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117774.14), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:44:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117774.13), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:45:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117750.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:46:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117727.84), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:47:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117693.61), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:48:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117719.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:49:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117699.71), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:50:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117720.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:51:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117766.6), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:52:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117753.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:53:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117792.26), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:54:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117792.26), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:55:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117786.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:56:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117786.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:56:26] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 11:57:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117765.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:58:13] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 11:58:19] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117765.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 11:59:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117753.6), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:00:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117765.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:01:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117758.94), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:02:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117753.58), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:03:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117733.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:04:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117719.28), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:05:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117719.28), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:06:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117714.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:07:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117714.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:08:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117698.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:09:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117733.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:10:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117754.37), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:11:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117747.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:12:17] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 12:12:22] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117727.86), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:13:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117739.81), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:14:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117751.58), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:15:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117731.09), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:16:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117731.08), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:17:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117751.58), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:18:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117742.48), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:19:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117779.13), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:20:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117812.03), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:21:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117802.29), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:22:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117802.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:23:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117812.03), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:24:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117780.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:25:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117769.85), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:26:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117769.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:27:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117769.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:28:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117769.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:29:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117769.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:30:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117769.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:31:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117777.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:32:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117805.81), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:33:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117805.81), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:34:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117805.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:35:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117822.56), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:36:03] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 12:36:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117822.56), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:37:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117822.94), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:38:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117825.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:39:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117825.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:40:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117850.02), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:41:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117845.37), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:42:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117827.47), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:43:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117827.6), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:44:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117827.6), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:45:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117827.6), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:46:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117837.16), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:47:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117837.17), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:48:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117846.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:49:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117864.02), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:50:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117867.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:51:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117883.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:52:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117892.23), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:53:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117892.23), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:53:56] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 12:54:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117892.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:55:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117892.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:56:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117872.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:57:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117878.14), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:58:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117879.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 12:59:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117879.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:00:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117879.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:01:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117865.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:02:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117865.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:03:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117865.69), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:04:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117902.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:05:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117902.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:06:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117902.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:07:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117902.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:08:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117902.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:09:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117904.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:10:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117904.74), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:11:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117894.25), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:12:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117894.25), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:13:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117878.09), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:14:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117878.1), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:15:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117862.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:16:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117858.68), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:17:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117820.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:18:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117822.54), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:19:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117822.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:20:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117822.54), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:21:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117822.54), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:22:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117822.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:23:20] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 13:23:25] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117820.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:24:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117818.57), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:25:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117815.07), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:26:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117810.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:27:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117827.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:28:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117832.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:29:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117832.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:30:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117832.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:31:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117832.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:32:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117855.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:33:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117894.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:34:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117894.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:35:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117894.94), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:36:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117894.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:37:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117865.25), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:38:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117865.25), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:39:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117865.25), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:40:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117880.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:41:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117866.48), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:42:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117748.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:43:16] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 13:43:21] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117779.94), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:44:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117779.93), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:45:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117775.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:46:04] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 13:46:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117731.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:47:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117714.57), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:48:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117753.67), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:49:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117732.24), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:50:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117722.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:51:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117719.25), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:52:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117718.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:53:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117718.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:54:15] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 13:54:20] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117713.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:55:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117712.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:56:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117746.64), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:57:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117765.44), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:57:34] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 13:58:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117746.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 13:59:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117760.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:00:23] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 14:00:28] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117759.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:01:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117759.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:02:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117742.32), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:03:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117754.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:04:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117775.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:05:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117775.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:06:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117775.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:07:01] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 14:07:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117788.32), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:08:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117777.74), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:09:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117785.66), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:10:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117785.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:11:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117785.66), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:12:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117764.19), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:13:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117768.16), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:14:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117783.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:15:12] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 14:15:17] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117783.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:16:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117783.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:17:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117790.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:18:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117775.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:18:46] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 14:19:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117778.13), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:20:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117797.81), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:20:34] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 14:21:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117797.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:22:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117797.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:23:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117823.73), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:24:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117836.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:25:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117850.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:26:11] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 14:26:26] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 14:26:31] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117861.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:27:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117861.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:28:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117861.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:29:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117861.12), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:30:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117861.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:31:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117861.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:32:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117861.12), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:33:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117861.12), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:34:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117883.34), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:35:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117899.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:36:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117899.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:37:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117930.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:37:55] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 14:38:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117929.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:39:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117968.15), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:40:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117968.14), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:41:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117953.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:42:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117953.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:43:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117963.85), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:44:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117978.96), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:45:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117963.87), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:46:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117902.93), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:47:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117902.93), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:48:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117902.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:49:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117902.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:50:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117902.93), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:51:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117923.74), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:52:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117911.86), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:53:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117911.86), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:54:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117888.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:55:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117888.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:56:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117888.51), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:57:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117888.51), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:57:37] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 14:58:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117888.51), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 14:58:53] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 14:59:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117888.51), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:00:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117888.51), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:00:41] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 15:01:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117888.51), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:02:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117888.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:03:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117876.71), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:04:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117852.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:05:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117861.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:06:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117885.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:07:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117900.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:08:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117884.49), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:09:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117884.48), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:10:23] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 15:10:28] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117884.49), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:11:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117884.48), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:12:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117884.48), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:13:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117884.48), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:14:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117884.48), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:15:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117884.49), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:16:01] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 15:16:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117917.37), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:17:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117949.72), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:18:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117949.71), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:19:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117917.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:20:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117926.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:20:52] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 15:21:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117926.96), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:21:38] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 15:22:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117938.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:23:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117959.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:24:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117966.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:25:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117984.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:26:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117990.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:27:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117990.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:28:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117989.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:29:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117989.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:30:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117990.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:31:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117990.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:32:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117989.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:33:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117990.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:34:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117989.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:35:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117997.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:36:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117997.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:30:03] 模型加载: 成功加载模型和配置
[2025-07-13 18:30:04] 数据获取: 初始数据获取成功，1000条记录
[2025-07-13 18:30:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117800.01, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:31:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117800.01, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:32:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117787.19, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:33:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117763.52, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:34:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117762.95, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:35:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117783.96, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:36:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117783.96, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:37:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117783.95, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:38:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117783.96, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:39:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117783.96, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:39:26] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 18:40:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117783.95, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:41:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117783.95, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:42:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117783.95, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:43:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117783.95, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:44:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117783.95, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:45:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117783.95, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:46:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117783.95, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:47:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117783.96, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:48:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117753.69, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:49:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117764.99, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:50:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117783.95, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:51:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117783.94, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:52:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117773.63, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:52:28] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 18:52:58] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 18:53:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117773.62, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:54:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117773.62, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:55:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.5227817745803356, 'price': 117773.62, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:56:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117746.81, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:57:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117746.81, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:58:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117746.81, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 18:59:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117746.82, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:00:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117748.38, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:01:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117764.48, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:02:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117783.94, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:03:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117792.98, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:04:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117792.99, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:04:59] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 19:05:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117812.93, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:06:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4111111111111111, 'price': 117876.66, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:07:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4111111111111111, 'price': 117871.62, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:08:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4111111111111111, 'price': 117883.38, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:09:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4111111111111111, 'price': 117883.38, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:10:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4111111111111111, 'price': 117883.38, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:11:23] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 19:11:38] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 19:11:43] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4111111111111111, 'price': 117883.39, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:12:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4111111111111111, 'price': 117895.15, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:13:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4111111111111111, 'price': 117895.16, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:14:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117900.0, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:15:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117900.0, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:15:28] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 19:16:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117930.79, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:17:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117924.87, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:18:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117924.88, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:19:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117918.68, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:20:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117908.39, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:20:50] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-13 19:21:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117881.02, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:22:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117881.02, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:23:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117863.69, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 19:24:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': 0.4852670349907919, 'price': 117868.23, 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
