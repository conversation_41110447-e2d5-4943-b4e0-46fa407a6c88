#!/usr/bin/env python3
"""
backtestv3.py 使用示例
展示如何使用修改后的回测脚本（支持东8区时间）
"""

import subprocess
import sys
import os

def run_backtest_example():
    """运行回测示例"""
    print("="*60)
    print("backtestv3.py 使用示例（东8区时间支持）")
    print("="*60)
    
    # 检查必要文件是否存在
    required_files = [
        'btcusd_1-min_data.csv',
        'btc_prediction_model.joblib', 
        'model_config.json',
        'backtestv3.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("错误：缺少必要文件:")
        for file in missing_files:
            print(f"  - {file}")
        print("\n请确保所有必要文件都存在后再运行。")
        return
    
    print("所有必要文件检查通过！\n")
    
    # 示例1：使用行号启动
    print("示例1：从第5000行开始回测")
    print("命令：python backtestv3.py btcusd_1-min_data.csv --start-row 5000 --speed 0.05")
    print("说明：从数据的第5000行开始，每0.05秒处理一行数据\n")
    
    # 示例2：使用北京时间启动
    print("示例2：从指定北京时间开始回测")
    print("命令：python backtestv3.py btcusd_1-min_data.csv --start-time '2025-07-08 14:30:00' --speed 0.1")
    print("说明：从北京时间2025年7月8日14:30开始回测\n")
    
    # 示例3：快速回测
    print("示例3：快速回测")
    print("命令：python backtestv3.py btcusd_1-min_data.csv --start-row 10000 --speed 0.01")
    print("说明：快速回测，每0.01秒处理一行数据\n")
    
    # 询问用户是否要运行示例
    choice = input("是否要运行示例1（从第5000行开始，较慢速度）？(y/n): ").lower().strip()
    
    if choice == 'y' or choice == 'yes':
        print("\n开始运行回测示例...")
        print("提示：按 Ctrl+C 可以随时停止回测")
        print("-" * 50)
        
        try:
            # 运行回测
            cmd = [
                sys.executable, 'backtestv3.py', 
                'btcusd_1-min_data.csv',
                '--start-row', '5000',
                '--speed', '0.1'
            ]
            
            subprocess.run(cmd, check=True)
            
        except subprocess.CalledProcessError as e:
            print(f"回测运行出错: {e}")
        except KeyboardInterrupt:
            print("\n回测被用户中断")
        except FileNotFoundError:
            print("错误：找不到 backtestv3.py 文件")
    else:
        print("跳过示例运行")
    
    print("\n" + "="*60)
    print("时区转换说明：")
    print("- 所有显示的时间都是北京时间（东8区）")
    print("- 用户输入的 --start-time 参数也应该是北京时间")
    print("- 例如：'2025-07-08 14:30:00' 表示北京时间下午2:30")
    print("="*60)

if __name__ == '__main__':
    run_backtest_example()
