# ===============================================
#  第一部分：导入所有需要的库 (Imports)
# ===============================================
import pandas as pd
import numpy as np
from numba import njit
from backtesting import Backtest, Strategy
from backtesting.lib import crossover

# ===============================================
#  第二部分：定义所有工具函数 (Utility Functions)
# ===============================================

def ema(series, length):
    """计算指数移动平均线 (EMA)"""
    return series.ewm(span=length, adjust=False).mean()

def dema(series, length):
    """计算双指数移动平均线 (DEMA)"""
    ema1 = ema(series, length)
    ema2 = ema(ema1, length)
    return 2 * ema1 - ema2

def stdev(series, length):
    """计算标准差"""
    return series.rolling(window=length).std()

def sma(series, length):
    """计算简单移动平均线 (SMA)"""
    return series.rolling(window=length).mean()

def rsi(series, length=14):
    """计算相对强弱指数 (RSI)"""
    delta = series.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=length).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=length).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

@njit
def calculate_oc_numba(tt1_values):
    """
    使用 Numba 加速计算 OC 趋势状态指标
    """
    n = len(tt1_values)
    b5 = np.full(n, np.nan)
    oc = np.full(n, np.nan)
    p = 1.0
    first_valid_index = -1
    for i in range(n):
        if not np.isnan(tt1_values[i]):
            first_valid_index = i
            b5[i] = tt1_values[i]
            oc[i] = 0.0
            break
    if first_valid_index == -1:
        return b5, oc
    cum_abs_change = 0.0
    for i in range(first_valid_index + 1, n):
        cum_abs_change += abs(tt1_values[i] - b5[i - 1])
        n5 = i - first_valid_index
        a15_val = (cum_abs_change / n5) * p if n5 > 0 else 0
        if tt1_values[i] > b5[i - 1] + a15_val:
            b5[i] = tt1_values[i]
        elif tt1_values[i] < b5[i - 1] - a15_val:
            b5[i] = tt1_values[i]
        else:
            b5[i] = b5[i - 1]
        if b5[i] > b5[i - 1]:
            oc[i] = 1.0
        elif b5[i] < b5[i - 1]:
            oc[i] = -1.0
        else:
            oc[i] = oc[i - 1]
    return b5, oc

def prepare_multitimeframe_data(filepath, timeframes, params):
    """
    核心函数：加载数据、重采样、计算所有时间周期的信号，并合并成一个最终的DataFrame。
    """
    base_tf = timeframes[0]
    
    try:
        df_base = pd.read_csv(filepath)
    except FileNotFoundError:
        print(f"错误：找不到数据文件 '{filepath}'")
        return None
        
    # 基础数据清洗
    if 'datetime' not in df_base.columns and 'Timestamp' in df_base.columns:
        if df_base['Timestamp'].iloc[0] > 10**12: df_base['Timestamp'] = df_base['Timestamp'] // 1000
        df_base['datetime'] = pd.to_datetime(df_base['Timestamp'], unit='s')
    df_base.set_index('datetime', inplace=True)
    
    required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    for col in required_cols:
        if col.lower() in df_base.columns:
            df_base.rename(columns={col.lower(): col}, inplace=True)
        if col not in df_base.columns:
             raise ValueError(f"数据文件中缺少必需的列: {col}")
        df_base[col] = pd.to_numeric(df_base[col], errors='coerce')
    
    df_final = df_base[required_cols].copy().dropna()
    
    # 循环计算每个时间周期的指标
    for tf in timeframes:
        print(f"  正在处理 {tf} 周期...")
        if tf == base_tf:
            df_tf = df_final.copy()
        else:
            agg_rules = {'Open': 'first', 'High': 'max', 'Low': 'min', 'Close': 'last', 'Volume': 'sum'}
            df_tf = df_final.resample(tf, label='right', closed='right').agg(agg_rules).dropna()
        
        # 指标计算
        df = df_tf.copy()
        df['price_spread'] = stdev(df['High'] - df['Low'], length=params['window_len'])
        v = (np.sign(df['Close'].diff()) * df['Volume']).fillna(0).cumsum()
        df['v'] = v
        df['smooth'] = sma(df['v'], length=params['v_len'])
        df['v_spread'] = stdev(df['v'] - df['smooth'], length=params['window_len'])
        df['shadow'] = (df['v'] - df['smooth']) / df['v_spread'] * df['price_spread']
        df['out'] = np.where(df['shadow'] > 0, df['High'] + df['shadow'], df['Low'] + df['shadow'])
        df['obvema'] = df['out'] if params['obv_len'] == 1 else ema(df['out'], length=params['obv_len'])
        df['ma'] = dema(df['obvema'], length=params['ma_len'])
        df['slow_ma'] = ema(df['Close'], length=params['slow_length'])
        df['macd_custom'] = df['ma'] - df['slow_ma']
        df['tt1'] = 2 * df['macd_custom'] - df['macd_custom'].shift(1)
        _, oc_vals = calculate_oc_numba(df['tt1'].to_numpy())
        df['oc'] = oc_vals
        df['rsi'] = rsi(df['Close'], length=params['rsi_len'])

        # 生成交易触发信号 (Trigger)
        prev_oc = df['oc'].shift(1)
        prev_rsi = df['rsi'].shift(1)
        buy_cond = (prev_oc == -1) & (df['oc'] == 1) & (prev_rsi <= params['rsi_os']) & (df['rsi'] > params['rsi_os'])
        sell_cond = (prev_oc == 1) & (df['oc'] == -1) & (prev_rsi >= params['rsi_ob']) & (df['rsi'] < params['rsi_ob'])
        df['signal'] = np.select([buy_cond, sell_cond], [1, -1], default=0)
        
        # 将结果合并回主DataFrame
        tf_results = df[['signal', 'oc']]
        tf_results.columns = [f'signal_{tf}', f'oc_{tf}']
        df_final = df_final.join(tf_results)

    # 向前填充，使每个时间点都有所有周期的状态
    for tf in timeframes:
        for col_prefix in ['signal', 'oc']:
            col_name = f"{col_prefix}_{tf}"
            if col_name in df_final.columns:
                df_final[col_name] = df_final[col_name].ffill().fillna(0).astype(int)
    
    return df_final


# ===============================================
#  第三部分：定义策略类 (Strategy Class)
# ===============================================
class MultiTimeframeStrategy(Strategy):
    """
    多时间周期共振策略
    - 使用基础周期(base_tf)的信号作为触发器
    - 使用所有周期的OC状态作为过滤器
    - 当趋势一致性被打破时平仓
    """
    # 策略参数
    trade_size = 0.9  # 每次交易使用90%的资金
    
    # 这些参数将在运行前回测前动态设置
    timeframes = []
    base_tf = ''

    def init(self):
        """
        初始化策略，将预计算的信号列注册为可访问的指标。
        """
        for tf in self.timeframes:
            # 基础周期的触发信号
            signal_col_name = f'signal_{tf}'
            signal_series = pd.Series(self.data.df[signal_col_name])
            setattr(self, signal_col_name, self.I(lambda x: x, signal_series, name=signal_col_name))
            
            # 所有周期的状态信号
            oc_col_name = f'oc_{tf}'
            oc_series = pd.Series(self.data.df[oc_col_name])
            setattr(self, oc_col_name, self.I(lambda x: x, oc_series, name=oc_col_name))

    def next(self):
        """
        在每个时间点上执行的交易逻辑
        """
        # --- 共振信号判断 ---
        buy_trigger = getattr(self, f'signal_{self.base_tf}')[-1] == 1
        sell_trigger = getattr(self, f'signal_{self.base_tf}')[-1] == -1

        is_bullish_state = all(getattr(self, f'oc_{tf}')[-1] == 1 for tf in self.timeframes)
        is_bearish_state = all(getattr(self, f'oc_{tf}')[-1] == -1 for tf in self.timeframes)
        
        # --- 平仓逻辑 ---
        if self.position.is_long and not is_bullish_state:
            self.position.close()
        elif self.position.is_short and not is_bearish_state:
            self.position.close()

        # --- 开仓逻辑 ---
        if not self.position:
            if buy_trigger and is_bullish_state:
                self.buy(size=self.trade_size)
            elif sell_trigger and is_bearish_state:
                self.sell(size=self.trade_size)


# ===============================================
#  第四部分：主程序入口 (Main Execution Block)
# ===============================================
if __name__ == '__main__':
    
    # --- 步骤 1: 定义要测试的时间周期组合和固定参数 ---
    timeframe_candidates = [
        ['5min', '15min'],
        ['5min', '30min'],
        ['5min', '15min', '1h'],
        ['15min', '1h'],
        ['15min', '1h', '4h'],
        ['30min', '2h', '6h'],
    ]
    
    fixed_params = {
        'window_len': 28, 'v_len': 14, 'obv_len': 1, 
        'ma_len': 9, 'slow_length': 26, 'rsi_len': 14, 
        'rsi_ob': 70, 'rsi_os': 30
    }
    
    data_filepath = "hyper.csv" # <--- 修改为你自己的数据文件名
    
    # 用于存储所有组合的回测结果
    all_results = []

    print("===== 开始寻找最佳时间周期组合 =====")

    # --- 步骤 2: 外层循环，遍历每个候选组合 ---
    for tf_combination in timeframe_candidates:
        print(f"\n--- 正在测试周期组合: {tf_combination} ---")
        
        try:
            # --- 步骤 3a: 为当前组合准备数据 ---
            final_data = prepare_multitimeframe_data(
                filepath=data_filepath, 
                timeframes=tf_combination, 
                params=fixed_params
            )

            if final_data is None or final_data.empty or len(final_data) < 100:
                print(f"数据准备失败或数据量过少({len(final_data) if final_data is not None else 0})，跳过组合: {tf_combination}")
                continue

            # --- 步骤 3b: 运行回测 ---
            MultiTimeframeStrategy.timeframes = tf_combination
            MultiTimeframeStrategy.base_tf = tf_combination[0]

            bt = Backtest(
                final_data,
                MultiTimeframeStrategy,
                cash=10000.0,
                commission=.001,
                exclusive_orders=True
            )

            stats = bt.run()
            
            # --- 步骤 3c: 记录结果 ---
            result_summary = {
                'Timeframes': str(tf_combination),
                'Sharpe Ratio': stats['Sharpe Ratio'],
                'Return [%]': stats['Return [%]'],
                'Max. Drawdown [%]': stats['Max. Drawdown [%]'],
                'Win Rate [%]': stats['Win Rate [%]'],
                '# Trades': stats['# Trades']
            }
            all_results.append(result_summary)
            
            print(f"组合 {tf_combination} 的夏普比率: {stats['Sharpe Ratio']:.2f}, 回报率: {stats['Return [%]']:.2f}%")

        except Exception as e:
            print(f"测试组合 {tf_combination} 时发生严重错误: {e}")
            import traceback
            traceback.print_exc()
            continue

    # --- 步骤 4: 结果比较和报告 ---
    if not all_results:
        print("\n所有测试均未产生有效结果。")
    else:
        results_df = pd.DataFrame(all_results)
        results_df.sort_values(by='Sharpe Ratio', ascending=False, inplace=True)

        print("\n\n===== 所有时间周期组合的回测结果排名 (按夏普比率) =====")
        print(results_df.to_string())

        best_combination_stats = results_df.iloc[0]
        print(f"\n\n*** 最佳时间周期组合是: {best_combination_stats['Timeframes']} ***")
        print("其表现为:")
        print(best_combination_stats)