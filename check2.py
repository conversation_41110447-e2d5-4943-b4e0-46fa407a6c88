# debug_discrepancy_final.py
import pandas as pd
import joblib
import json
import warnings

warnings.simplefilter(action='ignore', category=FutureWarning)

from feature import calculate_all_features
import config

def debug_timestamp_discrepancy(target_timestamp_str):
    """
    【最终版】法医脚本，通过“先计算再切片”的正确方式来模拟 train.py。
    """
    print("="*60)
    print(f"🕵️  终极法医鉴定开始，目标时间戳: {target_timestamp_str}")
    print("="*60)

    # --- 1. 加载所有工具和原始数据 ---
    model_config = json.load(open(config.CONFIG_OUTPUT_FILE, 'r'))
    raw_df = pd.read_csv(config.SOURCE_DATA_FILE)
    
    # --- 2. 执行与 train.py 完全相同的预处理 ---
    print("--- 正在执行与 train.py 一致的预处理 ---")
    raw_df['Timestamp'] = pd.to_datetime(raw_df['Timestamp'], unit='s')
    raw_df.set_index('Timestamp', inplace=True)
    raw_df.rename(columns={'Open':'open', 'High':'high', 'Low':'low', 'Close':'close', 'Volume':'volume'}, inplace=True)
    if config.START_DATE:
        print(f"    根据config筛选数据，起始日期: {config.START_DATE}")
        raw_df = raw_df.loc[config.START_DATE:]
    raw_df.sort_index(inplace=True)
    full_df = raw_df
    
    target_timestamp = pd.to_datetime(target_timestamp_str)
    feature_list = model_config['feature_list']

    # --- 3. 【A部分】正确模拟 "train.py" 上下文 ---
    #      核心逻辑: 先在完整数据集上计算所有特征，然后再提取目标行
    print("\n--- (A) 正在以正确方式模拟 [train.py] 上下文 (先计算, 后切片)...")
    print("    正在对完整历史数据计算特征，这可能需要一些时间...")
    df_with_all_features = calculate_all_features(full_df.copy())
    
    if target_timestamp not in df_with_all_features.index:
        print(f"错误：目标时间戳 {target_timestamp} 在特征计算后不存在。")
        return
        
    features_row_train = df_with_all_features.loc[target_timestamp][feature_list]
    print(f"--- [train.py] 黄金标准特征向量 (时间: {target_timestamp}) ---")
    print(features_row_train.to_string())
    print("-" * 50)
    
    # --- 4. 【B部分】模拟 "backtest_simulator.py" 上下文 ---
    #      核心逻辑: 先切片出有限缓冲区，再计算特征
    print("\n--- (B) 正在模拟 [backtest_simulator.py] 上下文 (先切片, 后计算)...")
    buffer_size = 2000 # 确保这个值和你的模拟器一致
    simulator_start_time = target_timestamp - pd.Timedelta(minutes=buffer_size - 1)
    df_for_simulator_context = full_df.loc[simulator_start_time:target_timestamp]
    
    features_simulator_context = calculate_all_features(df_for_simulator_context.copy())
    
    if features_simulator_context.empty or target_timestamp not in features_simulator_context.index:
        print("错误：目标时间戳在 'simulator' 上下文中被删除。")
        return

    features_row_simulator = features_simulator_context.loc[target_timestamp][feature_list]
    print(f"--- [backtest_simulator] 特征向量 (时间: {target_timestamp}) ---")
    print(features_row_simulator.to_string())
    print("-" * 50)

    # --- 5. 最终的自动差异报告 ---
    print("\n--- 最终自动差异报告 ---")
    comparison = pd.DataFrame({'Golden_Standard (A)': features_row_train, 'Simulator (B)': features_row_simulator})
    comparison['Difference'] = abs(comparison['Golden_Standard (A)'] - comparison['Simulator (B)'])
    differing_features = comparison[comparison['Difference'] > 1e-9]

    if differing_features.empty:
        print("\n✅ 法医鉴定通过：两个上下文计算出的特征向量完全一致。")
        print("   这意味着你的 backtest_simulator 的缓冲区大小是足够的！")
    else:
        print("\n❌ 发现特征不一致！这确认了 backtest_simulator 的缓冲区不足。")
        print("   有差异的特征如下:")
        print(differing_features)
        print("\n   解决方案：请显著增大 backtest_simulator.py 中的 INITIAL_BUFFER_SIZE (例如设为2000或更高)。")

if __name__ == '__main__':
    # 使用你报告的那个时间点
    TARGET_TIMESTAMP = "2025-07-08 00:01:00"
    
    debug_timestamp_discrepancy(TARGET_TIMESTAMP)