# train.py (带自检功能的最终版)

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.calibration import CalibratedClassifierCV
import joblib
import json
import warnings

# 忽略 pandas 的 FutureWarning
warnings.simplefilter(action='ignore', category=FutureWarning)

# 从我们自己的模块中导入配置和函数
try:
    import config
    from feature import calculate_all_features
except ImportError:
    print("错误：无法导入 'config' 或 'feature_engineering' 模块。")
    exit()

def preprocess_data(df):
    """进行基础的数据预处理。"""
    print("--> 步骤 1/8: 正在预处理原始数据...")
    df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s') 
    df.set_index('Timestamp', inplace=True)
    df.rename(columns={'Open':'open', 'High':'high', 'Low':'low', 'Close':'close', 'Volume':'volume'}, inplace=True)
    if config.START_DATE:
        df = df.loc[config.START_DATE:]
    if config.END_DATE:
        df = df.loc[:config.END_DATE]
    print(f"    数据筛选后范围: {df.index.min()} -> {df.index.max()}")
    df.dropna(inplace=True)
    df.sort_index(inplace=True)
    df['future_price_10min'] = df['close'].shift(-10)
    df['label'] = (df['future_price_10min'] > df['close']).astype(int)
    df.dropna(inplace=True)
    return df

def find_best_threshold(model, X_val, y_val):
    """在验证集上寻找最优的决策阈值。"""
    print("--> 步骤 5/8: 正在验证集上寻找最优阈值...")
    # ... (此函数内容与之前版本完全相同，为简洁省略) ...
    val_probabilities = model.predict_proba(X_val)[:, 1]
    thresholds_to_test = np.arange(0.56, 0.75, 0.01)
    best_score = -np.inf
    best_threshold = 0.5
    for threshold in thresholds_to_test:
        guesses = np.select([val_probabilities > threshold, val_probabilities < (1 - threshold)], [1, -1], default=0)
        actuals = y_val.values
        wins = np.sum((guesses == 1) & (actuals == 1)) + np.sum((guesses == -1) & (actuals == 0))
        losses = np.sum(guesses != 0) - wins
        score = wins * 0.8 - losses * 1.0
        if score > best_score:
            best_score = score
            best_threshold = threshold
    print(f"    验证集最优得分: {best_score:.2f} (在阈值 = {best_threshold:.2f} 时取得)")
    return best_threshold

def evaluate_on_test_set(model, test_df, features, best_threshold, title):
    """在测试集上进行详细评估并打印日志。"""
    print(f"\n" + "#"*80)
    print(f"### {title} ###")
    print("#"*80)
    
    X_test = test_df[features]
    if X_test.empty:
        print("    警告：测试集为空，无法进行评估。")
        return
    if not X_test.empty:
        first_test_vector = X_test.iloc[0]
        print("\n--- 打印首个测试样本的特征向量 ---")
        print(f"时间戳: {first_test_vector.name}")
        # 使用 to_string() 保证所有列都能被打印出来
        print(first_test_vector.to_string())
        print("------------------------------------\n")
    test_probabilities = model.predict_proba(X_test)[:, 1]
    test_prob_series = pd.Series(test_probabilities, index=X_test.index)
    
    # ... (此函数的核心打印逻辑与之前版本完全相同，为简洁省略) ...
    test_score, trades_made, wins, losses = 0, 0, 0, 0
    print(f"{'时间':<22}{'操作':<8}{'信心':<8}{'当前价格':<12}{'十分钟后价格':<15}{'结果':<12}{'得分'}")
    print("-" * 80)
    for timestamp, row in test_df.iterrows():
        if timestamp not in test_prob_series: continue
        prob = test_prob_series.loc[timestamp]
        action_str, result_str, score_for_this_trade, guess = "放弃", "-", 0.0, None
        if prob > best_threshold:
            guess, action_str = 1, "\033[92m猜涨\033[0m"
        elif prob < (1 - best_threshold):
            guess, action_str = 0, "\033[91m猜跌\033[0m"
        if guess is not None:
            trades_made += 1
            if guess == row['label']:
                wins, test_score, score_for_this_trade, result_str = wins + 1, test_score + 0.8, 0.8, "猜对了✅"
            else:
                losses, test_score, score_for_this_trade, result_str = losses + 1, test_score - 1.0, -1.0, "猜错了❌"
        # 只打印有操作的行，避免刷屏
        if guess is not None:
             print(f"{str(timestamp):<22}{action_str:<15}{prob:<8.2%}{row['close']:<12.2f}{row['future_price_10min']:<15.2f}{result_str:<12}{score_for_this_trade:+.1f}")
    print("-" * 80)
    win_rate = (wins / trades_made * 100) if trades_made > 0 else 0
    print(f"统计摘要: 总得分={test_score:.2f}, 猜测次数={trades_made}, 胜率={win_rate:.2f}%")


def main():
    """主训练流程"""
    # 1-3. 数据加载、预处理、特征工程
    raw_df = pd.read_csv(config.SOURCE_DATA_FILE)
    processed_df = preprocess_data(raw_df)
    print("--> 步骤 2/8: 正在计算特征...")
    df_with_features = calculate_all_features(processed_df)
    features = [col for col in df_with_features.columns if col not in config.EXCLUDED_COLUMNS and col != config.TARGET_COLUMN]
    
    # 4. 数据划分
    print("--> 步骤 3/8: 正在按日期划分数据集...")
    train_df = df_with_features.loc[:config.TRAIN_END_DATE]
    val_df = df_with_features.loc[config.TRAIN_END_DATE:config.VALIDATION_END_DATE].iloc[1:]
    test_df = df_with_features.loc[config.VALIDATION_END_DATE:].iloc[1:]
    X_train, y_train = train_df[features], train_df[config.TARGET_COLUMN]
    X_val, y_val = val_df[features], val_df[config.TARGET_COLUMN]
    print(f"    训练集: {len(X_train)}, 验证集: {len(X_val)}, 测试集: {len(test_df)}")

    # 5. 训练和校准
    print("--> 步骤 4/8: 正在训练和校准模型...")
    lgbm = lgb.LGBMClassifier(**config.LGBM_PARAMS)
    lgbm.fit(X_train, y_train, eval_set=[(X_val, y_val)], eval_metric='auc', callbacks=[lgb.early_stopping(stopping_rounds=config.EARLY_STOPPING_ROUNDS, verbose=False)])
    calibrated_model = CalibratedClassifierCV(lgbm, method='isotonic', cv='prefit')
    calibrated_model.fit(X_val, y_val)

    # 6. 寻找最优阈值
    best_threshold = find_best_threshold(calibrated_model, X_val, y_val)

    # 7. 【A部分】使用内存中的模型进行评估
    evaluate_on_test_set(calibrated_model, test_df, features, best_threshold, title="(A) 使用内存中的模型进行评估")

    # 8. 保存模型和配置
    print("\n--> 步骤 7/8: 正在保存最终模型和配置文件...")
    joblib.dump(calibrated_model, config.MODEL_OUTPUT_FILE)
    output_config = {'best_threshold': best_threshold, 'feature_list': features}
    with open(config.CONFIG_OUTPUT_FILE, 'w') as f:
        json.dump(output_config, f, indent=4)
    print(f"    模型和配置已保存。")

    # 9. 【B部分】加载刚保存的模型进行验证
    print("\n--> 步骤 8/8: 正在加载已保存的模型进行交叉验证...")
    loaded_model = joblib.load(config.MODEL_OUTPUT_FILE)
    with open(config.CONFIG_OUTPUT_FILE, 'r') as f:
        loaded_config = json.load(f)
    
    # 使用从文件加载回来的模型和阈值再次评估
    evaluate_on_test_set(loaded_model, test_df, loaded_config['feature_list'], loaded_config['best_threshold'], title="(B) 使用从文件加载的模型进行评估")

    print("\n✅ 训练和自检流程全部完成！")

if __name__ == '__main__':
    main()