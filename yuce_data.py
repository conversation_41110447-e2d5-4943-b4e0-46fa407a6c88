import pandas as pd
import time
import os
import csv
import argparse

def simulate_live_data(source_file, start_row=0, speed=1.0):
    """
    从一个大的历史CSV文件中逐行读取数据，并将其追加到一个新的"实时"CSV文件中，
    以模拟实时数据流。

    :param source_file: 包含历史数据的源CSV文件路径。
    :param start_row: 从源文件的哪一行开始模拟（0是第一行数据）。
    :param speed: 每条新数据之间等待的秒数。
    """
    destination_file = 'live_btc_data.csv'

    # --- 1. 准备工作：加载源数据并重置目标文件 ---
    print(f"正在从 {source_file} 加载历史数据...")
    try:
        df_source = pd.read_csv(source_file)
    except FileNotFoundError:
        print(f"错误：找不到源文件 '{source_file}'！请确保文件名正确且文件存在。")
        return

    # 验证起始行是否有效
    if start_row >= len(df_source):
        print(f"错误：起始行 {start_row} 超出文件总行数 {len(df_source)}。")
        return

    # 如果目标文件存在，则删除它，确保一个干净的开始
    if os.path.exists(destination_file):
        os.remove(destination_file)
        print(f"已重置旧的 '{destination_file}' 文件。")

    # 获取表头并在目标文件中写入表头
    header = df_source.columns.tolist()
    with open(destination_file, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(header)
    
    print(f"模拟准备就绪。将从第 {start_row} 行开始，每 {speed} 秒产生一条新数据。")
    print("按 Ctrl+C 可以随时停止模拟。")
    print("-" * 50)
    
    # --- 2. 开始模拟循环 ---
    try:
        # 只迭代需要模拟的部分
        for index, row in df_source.iloc[start_row:].iterrows():
            # 以追加模式打开目标文件
            with open(destination_file, 'a', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(row)
            
            # 在控制台打印状态信息
            timestamp = pd.to_datetime(row['Timestamp'], unit='s')
            print(f"[{index}] 数据已生成 -> 时间: {timestamp}, 收盘价: {row['Close']}")
            
            # 等待指定的时间
            time.sleep(speed)
            
    except KeyboardInterrupt:
        print("\n" + "-" * 50)
        print("模拟被用户手动停止。")
    except Exception as e:
        print(f"\n发生未知错误: {e}")
        
    print(f"'{destination_file}' 已准备好，包含 {index - start_row + 1} 条模拟数据。")


if __name__ == '__main__':
    # 使用 argparse 来处理命令行参数，让脚本更灵活
    parser = argparse.ArgumentParser(
        description="模拟实时BTC数据流。",
        formatter_class=argparse.RawTextHelpFormatter # 格式化帮助信息
    )
    parser.add_argument(
        "source_file", 
        help="包含历史数据的源CSV文件路径。\n例如: btc_used_1-min_data.csv"
    )
    parser.add_argument(
        "--start-row", 
        type=int, 
        default=0, 
        help="从源文件的哪一行开始模拟 (默认: 0)。"
    )
    parser.add_argument(
        "--speed", 
        type=float, 
        default=1.0, 
        help="每条新数据之间的间隔秒数 (默认: 1.0)。\n设为0可以快速生成。"
    )
    
    args = parser.parse_args()
    
    simulate_live_data(args.source_file, args.start_row, args.speed)