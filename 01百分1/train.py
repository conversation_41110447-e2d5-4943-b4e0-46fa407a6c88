#!/usr/bin/env python3
"""
模型训练脚本
基于重构后的公共模块进行模型训练
"""

import sys
import os
import argparse
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.features import FeatureEngine
from core.data_processor import DataProcessor
from core.model_manager import ModelManager

def print_detailed_results(dataset_name: str, results: dict, y_true: pd.Series):
    """
    打印详细的评估结果

    Args:
        dataset_name: 数据集名称
        results: 评估结果字典
        y_true: 真实标签
    """
    print(f"\n{'='*20} {dataset_name}结果 {'='*20}")

    # 基本统计
    total_samples = len(y_true)
    positive_samples = (y_true == 1).sum()
    negative_samples = (y_true == 0).sum()
    positive_ratio = positive_samples / total_samples * 100

    print(f"📊 数据统计:")
    print(f"   总样本数: {total_samples}")
    print(f"   先涨1%样本: {positive_samples} ({positive_ratio:.1f}%)")
    print(f"   先跌1%样本: {negative_samples} ({100-positive_ratio:.1f}%)")

    # 模型性能
    print(f"\n🎯 模型性能:")
    print(f"   默认准确率: {results['accuracy']:.4f} ({results['accuracy']*100:.2f}%)")
    print(f"   最优阈值: {results['best_threshold']:.4f}")
    print(f"   最优准确率: {results['optimal_accuracy']:.4f} ({results['optimal_accuracy']*100:.2f}%)")

    # 混淆矩阵
    confusion_matrix = results['confusion_matrix']
    tn, fp, fn, tp = confusion_matrix[0][0], confusion_matrix[0][1], confusion_matrix[1][0], confusion_matrix[1][1]

    print(f"\n📈 混淆矩阵:")
    print(f"   真负例(TN): {tn}  假正例(FP): {fp}")
    print(f"   假负例(FN): {fn}  真正例(TP): {tp}")

    # 详细指标
    precision_0 = tn / (tn + fn) if (tn + fn) > 0 else 0  # 预测跌的准确率
    precision_1 = tp / (tp + fp) if (tp + fp) > 0 else 0  # 预测涨的准确率
    recall_0 = tn / (tn + fp) if (tn + fp) > 0 else 0     # 实际跌的召回率
    recall_1 = tp / (tp + fn) if (tp + fn) > 0 else 0     # 实际涨的召回率

    print(f"\n📋 详细指标:")
    print(f"   预测跌准确率: {precision_0:.4f} ({precision_0*100:.2f}%)")
    print(f"   预测涨准确率: {precision_1:.4f} ({precision_1*100:.2f}%)")
    print(f"   实际跌召回率: {recall_0:.4f} ({recall_0*100:.2f}%)")
    print(f"   实际涨召回率: {recall_1:.4f} ({recall_1*100:.2f}%)")

    # 分类报告
    print(f"\n📄 分类报告:")
    print(results['classification_report'])

    print("="*60)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="BTC百分比目标模型训练")
    parser.add_argument("--data-file", default="../btcusd_1-min_data.csv", help="数据文件路径")
    parser.add_argument("--save-data", action="store_true", help="保存预处理数据")
    parser.add_argument("--load-data", action="store_true", help="加载预处理数据")
    parser.add_argument("--model-name", default="btc_percentage_model", help="模型名称")
    parser.add_argument("--recent-days", type=int, default=None, help="只使用最近N天的数据进行训练")
    parser.add_argument("--show-results", action="store_true", help="显示验证集和测试集的详细结果")
    parser.add_argument("--use-enhanced-features", action="store_true", help="使用包含主动买卖特征的增强版特征列表")

    args = parser.parse_args()

    print("🚀 BTC百分比目标模型训练")
    print("="*50)

    # 初始化组件
    feature_engine = FeatureEngine()
    data_processor = DataProcessor()
    model_manager = ModelManager(model_dir="models")

    processed_data_file = f"data/processed_data_{args.model_name}.pkl"

    if args.load_data and os.path.exists(processed_data_file):
        # 加载预处理数据
        print("📂 加载预处理数据...")
        data = data_processor.load_processed_data(processed_data_file)

        X_train = data['X_train']
        X_val = data['X_val']
        X_test = data['X_test']
        y_train = data['y_train']
        y_val = data['y_val']
        y_test = data['y_test']
        feature_list = data['feature_list']

    else:
        # 完整的数据处理流程
        print("📊 开始数据处理流程...")

        # 1. 加载原始数据
        df = data_processor.load_data(args.data_file)

        # 1.5. 过滤最近N天的数据（如果指定）
        if args.recent_days:
            print(f"📅 过滤最近 {args.recent_days} 天的数据...")
            # 计算截止时间
            end_time = df.index.max()
            start_time = end_time - pd.Timedelta(days=args.recent_days)

            # 过滤数据
            df_filtered = df[df.index >= start_time].copy()

            print(f"   原始数据: {len(df)} 条")
            print(f"   过滤后数据: {len(df_filtered)} 条")
            print(f"   时间范围: {df_filtered.index.min()} 到 {df_filtered.index.max()}")

            if len(df_filtered) < 1000:
                print("⚠️  过滤后数据量较少，可能影响训练效果")

            df = df_filtered

        # 2. 验证数据质量
        if not data_processor.validate_data_quality(df):
            print("❌ 数据质量检查失败")
            return

        # 3. 计算特征
        print("🔧 计算特征...")
        df_with_features = feature_engine.calculate_features(df)

        # 4. 准备标签
        features_df, labels = data_processor.prepare_features_and_labels(
            df_with_features,
            up_threshold=0.01,
            down_threshold=0.01,
            max_minutes=240
        )

        # 5. 获取特征列表
        if args.use_enhanced_features:
            feature_list = feature_engine.get_enhanced_feature_list()
            print(f"🔧 使用增强特征列表 (包含主动买卖特征): {len(feature_list)} 个特征")
        else:
            feature_list = feature_engine.get_feature_list()
            print(f"🔧 使用原始特征列表 (与LightGBM_percentage.py一致): {len(feature_list)} 个特征")

        # 6. 验证特征完整性
        is_valid, missing_features = feature_engine.validate_features(features_df)
        if not is_valid:
            print(f"❌ 缺少特征: {missing_features}")
            return

        # 7. 分割数据
        X_train, X_val, X_test, y_train, y_val, y_test = data_processor.split_data(
            features_df, labels, train_ratio=0.7, val_ratio=0.15
        )

        # 8. 保存预处理数据（可选）
        if args.save_data:
            os.makedirs("data", exist_ok=True)
            data_to_save = {
                'X_train': X_train,
                'X_val': X_val,
                'X_test': X_test,
                'y_train': y_train,
                'y_val': y_val,
                'y_test': y_test,
                'feature_list': feature_list
            }
            data_processor.save_processed_data(data_to_save, processed_data_file)

    # 9. 训练模型
    print("\n🤖 开始模型训练...")
    model = model_manager.train_model(X_train, y_train, X_val, y_val, feature_list)

    # 10. 评估模型
    print("\n📊 评估模型性能...")

    # 评估测试集
    test_results = model_manager.evaluate_model(X_test, y_test, feature_list)

    # 如果需要显示详细结果，也评估验证集
    if args.show_results:
        print("\n📋 评估验证集性能...")
        val_results = model_manager.evaluate_model(X_val, y_val, feature_list)

        # 显示详细结果
        print_detailed_results("验证集", val_results, y_val)
        print_detailed_results("测试集", test_results, y_test)

    results = test_results

    # 11. 获取特征重要性
    importance_df = model_manager.get_feature_importance(feature_list)
    print("\n📈 特征重要性 (Top 10):")
    print(importance_df.head(10))

    # 保存特征重要性
    importance_file = f"models/feature_importance_{args.model_name}.csv"
    importance_df.to_csv(importance_file, index=False)
    print(f"✅ 特征重要性已保存: {importance_file}")

    # 12. 保存模型和配置
    config_data = {
        'model_type': 'LightGBM_percentage_target',
        'target_description': 'predict_first_1percent_move',
        'best_threshold': results['best_threshold'],
        'optimal_accuracy': results['optimal_accuracy'],
        'feature_list': feature_list,
        'up_threshold': 0.01,
        'down_threshold': 0.01,
        'max_minutes': 240
    }

    model_manager.save_model(args.model_name, config_data)

    print("\n🎉 模型训练完成!")
    print("="*50)

    # 显示数据使用情况
    if args.recent_days:
        print(f"📅 使用数据: 最近 {args.recent_days} 天")
    else:
        print(f"📅 使用数据: 全部历史数据")

    print(f"📊 模型性能:")
    print(f"   最优阈值: {results['best_threshold']:.4f}")
    print(f"   最优准确率: {results['optimal_accuracy']:.4f}")
    print(f"   特征数量: {len(feature_list)} (包含 {len([f for f in feature_list if 'taker' in f or 'buy_sell_pressure' in f])} 个主动买卖特征)")
    print(f"📁 模型文件: models/{args.model_name}.joblib")
    print(f"📁 配置文件: models/{args.model_name}_config.json")

    if not args.show_results:
        print("\n💡 使用 --show-results 参数查看验证集和测试集的详细结果")

    print("="*50)

if __name__ == "__main__":
    main()
