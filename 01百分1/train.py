#!/usr/bin/env python3
"""
模型训练脚本
基于重构后的公共模块进行模型训练
"""

import sys
import os
import argparse
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.features import FeatureEngine
from core.data_processor import DataProcessor
from core.model_manager import ModelManager

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="BTC百分比目标模型训练")
    parser.add_argument("--data-file", default="../btcusd_1-min_data.csv", help="数据文件路径")
    parser.add_argument("--save-data", action="store_true", help="保存预处理数据")
    parser.add_argument("--load-data", action="store_true", help="加载预处理数据")
    parser.add_argument("--model-name", default="btc_percentage_model", help="模型名称")

    args = parser.parse_args()

    print("🚀 BTC百分比目标模型训练")
    print("="*50)

    # 初始化组件
    feature_engine = FeatureEngine()
    data_processor = DataProcessor()
    model_manager = ModelManager(model_dir="models")

    processed_data_file = f"data/processed_data_{args.model_name}.pkl"

    if args.load_data and os.path.exists(processed_data_file):
        # 加载预处理数据
        print("📂 加载预处理数据...")
        data = data_processor.load_processed_data(processed_data_file)

        X_train = data['X_train']
        X_val = data['X_val']
        X_test = data['X_test']
        y_train = data['y_train']
        y_val = data['y_val']
        y_test = data['y_test']
        feature_list = data['feature_list']

    else:
        # 完整的数据处理流程
        print("📊 开始数据处理流程...")

        # 1. 加载原始数据
        df = data_processor.load_data(args.data_file)

        # 2. 验证数据质量
        if not data_processor.validate_data_quality(df):
            print("❌ 数据质量检查失败")
            return

        # 3. 计算特征
        print("🔧 计算特征...")
        df_with_features = feature_engine.calculate_features(df)

        # 4. 准备标签
        features_df, labels = data_processor.prepare_features_and_labels(
            df_with_features,
            up_threshold=0.01,
            down_threshold=0.01,
            max_minutes=240
        )

        # 5. 获取特征列表
        feature_list = feature_engine.get_feature_list()

        # 6. 验证特征完整性
        is_valid, missing_features = feature_engine.validate_features(features_df)
        if not is_valid:
            print(f"❌ 缺少特征: {missing_features}")
            return

        # 7. 分割数据
        X_train, X_val, X_test, y_train, y_val, y_test = data_processor.split_data(
            features_df, labels, train_ratio=0.7, val_ratio=0.15
        )

        # 8. 保存预处理数据（可选）
        if args.save_data:
            os.makedirs("data", exist_ok=True)
            data_to_save = {
                'X_train': X_train,
                'X_val': X_val,
                'X_test': X_test,
                'y_train': y_train,
                'y_val': y_val,
                'y_test': y_test,
                'feature_list': feature_list
            }
            data_processor.save_processed_data(data_to_save, processed_data_file)

    # 9. 训练模型
    print("\n🤖 开始模型训练...")
    model = model_manager.train_model(X_train, y_train, X_val, y_val, feature_list)

    # 10. 评估模型
    print("\n📊 评估模型性能...")
    results = model_manager.evaluate_model(X_test, y_test, feature_list)

    # 11. 获取特征重要性
    importance_df = model_manager.get_feature_importance(feature_list)
    print("\n📈 特征重要性 (Top 10):")
    print(importance_df.head(10))

    # 保存特征重要性
    importance_file = f"models/feature_importance_{args.model_name}.csv"
    importance_df.to_csv(importance_file, index=False)
    print(f"✅ 特征重要性已保存: {importance_file}")

    # 12. 保存模型和配置
    config_data = {
        'model_type': 'LightGBM_percentage_target',
        'target_description': 'predict_first_1percent_move',
        'best_threshold': results['best_threshold'],
        'optimal_accuracy': results['optimal_accuracy'],
        'feature_list': feature_list,
        'up_threshold': 0.01,
        'down_threshold': 0.01,
        'max_minutes': 240
    }

    model_manager.save_model(args.model_name, config_data)

    print("\n🎉 模型训练完成!")
    print("="*50)
    print(f"📊 模型性能:")
    print(f"   最优阈值: {results['best_threshold']:.4f}")
    print(f"   最优准确率: {results['optimal_accuracy']:.4f}")
    print(f"📁 模型文件: models/{args.model_name}.joblib")
    print(f"📁 配置文件: models/{args.model_name}_config.json")
    print("="*50)

if __name__ == "__main__":
    main()
