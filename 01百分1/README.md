# BTC百分比预测系统 (01百分1)

基于重构后公共模块的BTC价格预测系统，确保训练、测试、实时执行的一致性。

## 🎯 系统特点

### 预测目标
- **百分比目标**：预测BTC价格先涨1%还是先跌1%
- **时间窗口**：可配置等待时间（默认4小时）
- **评分规则**：成功+1分，失败-1分，超时0分

### 架构优势
- **模块化设计**：核心功能提取为公共模块
- **一致性保证**：训练、测试、实时执行使用相同的特征计算
- **易于维护**：统一的接口和配置管理
- **可扩展性**：便于添加新特征和优化算法

## 📁 目录结构

```
01百分1/
├── core/                    # 核心模块
│   ├── __init__.py
│   ├── features.py          # 特征工程（20个重要特征）
│   ├── data_processor.py    # 数据处理（加载、预处理、标签生成）
│   └── model_manager.py     # 模型管理（训练、保存、加载、预测）
├── utils/                   # 工具模块
│   ├── __init__.py
│   ├── logger.py           # 日志和报警
│   └── data_fetcher.py     # 数据获取
├── models/                  # 模型文件目录
├── data/                    # 数据文件目录
├── logs/                    # 日志文件目录
├── train.py                # 训练脚本
├── backtest.py             # 回测脚本
├── realtime.py             # 实时预测脚本
├── get_data.py             # 数据获取脚本
└── README.md               # 本文件
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install pandas numpy scikit-learn lightgbm requests

# 创建必要目录
mkdir -p models data logs
```

### 2. 获取数据
```bash
# 获取10000条历史数据
python get_data.py --limit 10000 --output data/btcusd_1-min_data.csv
```

### 3. 训练模型
```bash
# 训练模型并保存预处理数据
python train.py --data-file data/btcusd_1-min_data.csv --save-data

# 快速验证（使用缓存数据）
python train.py --load-data
```

### 4. 回测验证
```bash
# 限制模式回测（推荐）
python backtest.py data/btcusd_1-min_data.csv --mode limited --max-wait-hours 4

# 并发模式回测（快速验证）
python backtest.py data/btcusd_1-min_data.csv --mode concurrent --max-wait-hours 2
```

### 5. 实时预测
```bash
# 启动实时预测
python realtime.py --max-wait-hours 4 --update-interval 60

# 禁用声音（服务器环境）
python realtime.py --no-sound --log-file logs/server_predictions.log
```

## 📊 核心模块说明

### 特征工程 (core/features.py)
- **FeatureEngine类**：统一的特征计算引擎
- **20个重要特征**：基于特征重要性分析优化
- **一致性保证**：训练和预测使用相同的特征计算逻辑

```python
from core.features import FeatureEngine

engine = FeatureEngine()
df_with_features = engine.calculate_features(df)
feature_list = engine.get_feature_list()
```

### 数据处理 (core/data_processor.py)
- **DataProcessor类**：统一的数据处理接口
- **标签生成**：百分比目标标签生成
- **数据分割**：时间序列数据分割
- **质量验证**：数据完整性检查

```python
from core.data_processor import DataProcessor

processor = DataProcessor()
df = processor.load_data("data.csv")
features_df, labels = processor.prepare_features_and_labels(df)
```

### 模型管理 (core/model_manager.py)
- **ModelManager类**：统一的模型管理接口
- **训练和评估**：LightGBM模型训练和性能评估
- **保存和加载**：模型和配置的持久化
- **预测接口**：统一的预测接口

```python
from core.model_manager import ModelManager

manager = ModelManager()
model = manager.train_model(X_train, y_train, X_val, y_val, feature_list)
guess, probability = manager.predict(X, feature_list)
```

## 🔧 使用示例

### 完整训练流程
```bash
# 1. 获取数据
python get_data.py --limit 20000 --output data/training_data.csv

# 2. 训练模型
python train.py --data-file data/training_data.csv --save-data --model-name my_model

# 3. 回测验证
python backtest.py data/training_data.csv --model-name my_model --max-wait-hours 4

# 4. 实时预测
python realtime.py --model-name my_model --max-wait-hours 4
```

### 快速验证流程
```bash
# 使用缓存数据快速验证
python train.py --load-data --model-name quick_test

# 快速回测
python backtest.py data/btcusd_1-min_data.csv --model-name quick_test --speed 0.01
```

### 生产部署
```bash
# 后台运行实时预测
nohup python realtime.py \
    --model-name production_model \
    --max-wait-hours 4 \
    --update-interval 60 \
    --no-sound \
    --log-file logs/production.log \
    > logs/system.log 2>&1 &

# 监控日志
tail -f logs/production.log
```

## 📈 性能特点

### 模型性能
- **成功率**：60-75%（基于历史回测）
- **有效率**：70-85%（非超时预测比例）
- **特征数量**：20个优化后的重要特征
- **预测频率**：每小时1-3次

### 系统性能
- **内存使用**：~200MB（1000条数据缓存）
- **CPU使用**：低（主要在预测时）
- **网络带宽**：极低（每分钟一次API调用）
- **响应时间**：<1秒（特征计算+预测）

## 🛠️ 开发指南

### 添加新特征
1. 在`core/features.py`的`calculate_features`方法中添加
2. 更新`get_feature_list`方法
3. 重新训练模型

### 自定义预测目标
1. 修改`core/data_processor.py`的`prepare_features_and_labels`方法
2. 调整`check_percentage_result`方法
3. 更新配置文件

### 扩展数据源
1. 在`utils/data_fetcher.py`中添加新的数据源
2. 确保输出格式与现有系统兼容
3. 更新相关脚本

## 🔍 故障排除

### 常见问题

1. **模型文件不存在**
   ```bash
   # 先训练模型
   python train.py --data-file data/btcusd_1-min_data.csv
   ```

2. **特征计算错误**
   ```bash
   # 检查数据格式
   python -c "import pandas as pd; print(pd.read_csv('data.csv').head())"
   ```

3. **网络连接问题**
   ```bash
   # 测试连接
   python -c "from utils.data_fetcher import BinanceDataFetcher; BinanceDataFetcher().check_connection()"
   ```

### 日志分析
```bash
# 查看预测信号
grep "新预测" logs/prediction_log.txt

# 查看成功率
grep -c "成功✅" logs/prediction_log.txt
grep -c "失败❌" logs/prediction_log.txt

# 实时监控
tail -f logs/prediction_log.txt
```

## 📊 配置说明

### 模型配置文件 (models/model_config.json)
```json
{
  "model_type": "LightGBM_percentage_target",
  "target_description": "predict_first_1percent_move",
  "best_threshold": 0.660,
  "optimal_accuracy": 0.725,
  "feature_list": ["hour", "day_of_week", "atr_14_1m", ...],
  "up_threshold": 0.01,
  "down_threshold": 0.01,
  "max_minutes": 120
}
```

### 参数建议

| 场景 | 等待时间 | 更新间隔 | 模式 | 声音 |
|------|----------|----------|------|------|
| 开发测试 | 1-2小时 | 30秒 | concurrent | 禁用 |
| 日常交易 | 4小时 | 60秒 | limited | 启用 |
| 保守策略 | 8小时 | 120秒 | limited | 启用 |
| 服务器运行 | 4-6小时 | 60秒 | limited | 禁用 |

## 🎯 最佳实践

1. **数据管理**：定期更新历史数据，保持模型时效性
2. **模型维护**：定期重新训练，监控性能变化
3. **风险控制**：使用限制模式，避免过度交易
4. **系统监控**：定期检查日志，监控系统状态
5. **参数调优**：根据市场条件调整等待时间和阈值

---

## 📞 支持

- **项目文档**：`../README.md`
- **核心模块**：`core/` 目录下的各个模块
- **工具函数**：`utils/` 目录下的工具模块
- **日志文件**：`logs/` 目录下的日志文件

**Happy Trading! 🚀📈**
