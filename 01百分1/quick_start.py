#!/usr/bin/env python3
"""
快速开始脚本
一键运行完整的训练和验证流程
"""

import sys
import os
import subprocess
import argparse

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n🔄 {description}")
    print(f"💻 命令: {command}")
    print("-" * 50)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {description} 成功完成")
            if result.stdout:
                print("📤 输出:")
                print(result.stdout[-500:])  # 显示最后500字符
        else:
            print(f"❌ {description} 失败")
            if result.stderr:
                print("📤 错误:")
                print(result.stderr[-500:])
            return False
            
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False
    
    return True

def quick_start_demo():
    """快速开始演示"""
    print("🚀 BTC百分比预测系统 - 快速开始")
    print("="*60)
    print("这个脚本将演示完整的工作流程:")
    print("1. 获取历史数据")
    print("2. 训练模型")
    print("3. 运行回测")
    print("4. 显示结果")
    print("="*60)
    
    # 确保必要目录存在
    os.makedirs("data", exist_ok=True)
    os.makedirs("models", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    
    # 1. 获取数据
    if not run_command(
        "python get_data.py --limit 5000 --output data/demo_data.csv",
        "获取演示数据 (5000条记录)"
    ):
        return False
    
    # 2. 训练模型
    if not run_command(
        "python train.py --data-file data/demo_data.csv --model-name demo_model --save-data",
        "训练演示模型"
    ):
        return False
    
    # 3. 快速回测
    if not run_command(
        "python backtest.py data/demo_data.csv --model-name demo_model --mode limited --max-wait-hours 2 --speed 0.01 --start-row 3000",
        "运行快速回测"
    ):
        return False
    
    print("\n🎉 快速开始演示完成!")
    print("="*60)
    print("📊 接下来你可以:")
    print("1. 查看模型文件: models/demo_model.joblib")
    print("2. 查看配置文件: models/demo_model_config.json")
    print("3. 查看回测日志: logs/")
    print("4. 运行实时预测: python realtime.py --model-name demo_model")
    print("="*60)
    
    return True

def full_workflow():
    """完整工作流程"""
    print("🚀 BTC百分比预测系统 - 完整工作流程")
    print("="*60)
    
    # 确保必要目录存在
    os.makedirs("data", exist_ok=True)
    os.makedirs("models", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    
    # 1. 获取更多数据
    if not run_command(
        "python get_data.py --limit 20000 --output data/full_data.csv",
        "获取完整数据 (20000条记录)"
    ):
        return False
    
    # 2. 训练生产模型
    if not run_command(
        "python train.py --data-file data/full_data.csv --model-name production_model --save-data",
        "训练生产模型"
    ):
        return False
    
    # 3. 完整回测
    if not run_command(
        "python backtest.py data/full_data.csv --model-name production_model --mode limited --max-wait-hours 4 --speed 0.05 --start-row 15000",
        "运行完整回测"
    ):
        return False
    
    print("\n🎉 完整工作流程完成!")
    print("="*60)
    print("📊 生产就绪:")
    print("1. 模型文件: models/production_model.joblib")
    print("2. 配置文件: models/production_model_config.json")
    print("3. 启动实时预测: python realtime.py --model-name production_model --max-wait-hours 4")
    print("="*60)
    
    return True

def test_system():
    """测试系统"""
    print("🧪 系统测试")
    print("="*30)
    
    if not run_command(
        "python test_system.py",
        "运行系统测试"
    ):
        return False
    
    print("\n✅ 系统测试完成!")
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="BTC百分比预测系统快速开始")
    parser.add_argument("--mode", choices=["demo", "full", "test"], default="demo", 
                       help="运行模式: demo=快速演示, full=完整流程, test=系统测试")
    
    args = parser.parse_args()
    
    # 检查当前目录
    if not os.path.exists("core") or not os.path.exists("utils"):
        print("❌ 请在 01百分1 目录下运行此脚本")
        print("💡 正确用法: cd 01百分1 && python quick_start.py")
        return
    
    print(f"📍 当前目录: {os.getcwd()}")
    print(f"🎯 运行模式: {args.mode}")
    
    success = False
    
    if args.mode == "demo":
        success = quick_start_demo()
    elif args.mode == "full":
        success = full_workflow()
    elif args.mode == "test":
        success = test_system()
    
    if success:
        print("\n🎉 所有操作成功完成!")
        print("📚 更多信息请查看 README.md")
    else:
        print("\n❌ 操作过程中出现错误")
        print("🔍 请检查错误信息并重试")

if __name__ == "__main__":
    main()
