#!/usr/bin/env python3
"""
数据处理公共模块
统一数据加载、预处理、标签生成等功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timezone
from typing import Tuple, Optional
import pickle

class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        """初始化数据处理器"""
        pass
    
    def load_data(self, file_path: str) -> pd.DataFrame:
        """
        加载数据文件
        
        Args:
            file_path: 数据文件路径
            
        Returns:
            处理后的DataFrame
        """
        try:
            df = pd.read_csv(file_path)
            
            # 转换时间戳
            if 'Timestamp' in df.columns:
                df['Timestamp_dt'] = pd.to_datetime(df['Timestamp'], unit='s', utc=True).dt.tz_convert('Asia/Shanghai')
                df.set_index('Timestamp_dt', inplace=True, drop=False)
            
            # 标准化列名
            column_mapping = {
                'Open': 'open',
                'High': 'high', 
                'Low': 'low',
                'Close': 'close',
                'Volume': 'volume'
            }
            
            for old_col, new_col in column_mapping.items():
                if old_col in df.columns:
                    df = df.rename(columns={old_col: new_col})
            
            # 按时间排序
            df.sort_index(inplace=True)
            
            print(f"✅ 数据加载成功: {len(df)} 条记录")
            print(f"📅 时间范围: {df.index[0]} 到 {df.index[-1]}")
            
            return df
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            raise
    
    def prepare_features_and_labels(self, df: pd.DataFrame, 
                                  up_threshold: float = 0.01, 
                                  down_threshold: float = 0.01,
                                  max_minutes: int = 120) -> Tuple[pd.DataFrame, pd.Series]:
        """
        准备特征和标签
        
        Args:
            df: 原始数据
            up_threshold: 上涨阈值
            down_threshold: 下跌阈值
            max_minutes: 最大等待分钟数
            
        Returns:
            (特征DataFrame, 标签Series)
        """
        print("🔄 准备特征和标签...")
        
        # 计算标签
        labels = []
        for i in range(len(df)):
            if i + max_minutes >= len(df):
                labels.append(np.nan)
                continue
            
            current_price = df.iloc[i]['close']
            up_target = current_price * (1 + up_threshold)
            down_target = current_price * (1 - down_threshold)
            
            # 检查未来价格
            future_prices = df.iloc[i+1:i+max_minutes+1]['close'].values
            
            result = None
            for price in future_prices:
                if price >= up_target:
                    result = 1  # 先涨1%
                    break
                elif price <= down_target:
                    result = 0  # 先跌1%
                    break
            
            labels.append(result)
        
        df['target'] = labels
        
        # 移除无效标签的行
        valid_mask = df['target'].notna()
        df_clean = df[valid_mask].copy()
        
        print(f"✅ 标签生成完成")
        print(f"📊 有效样本: {len(df_clean)} / {len(df)}")
        print(f"📈 先涨1%: {(df_clean['target'] == 1).sum()}")
        print(f"📉 先跌1%: {(df_clean['target'] == 0).sum()}")
        
        return df_clean.drop('target', axis=1), df_clean['target']
    
    def split_data(self, df: pd.DataFrame, labels: pd.Series, 
                   train_ratio: float = 0.7, 
                   val_ratio: float = 0.15) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, 
                                                    pd.Series, pd.Series, pd.Series]:
        """
        按时间顺序分割数据
        
        Args:
            df: 特征数据
            labels: 标签数据
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            
        Returns:
            (train_df, val_df, test_df, train_labels, val_labels, test_labels)
        """
        n = len(df)
        train_end = int(n * train_ratio)
        val_end = int(n * (train_ratio + val_ratio))
        
        train_df = df.iloc[:train_end]
        val_df = df.iloc[train_end:val_end]
        test_df = df.iloc[val_end:]
        
        train_labels = labels.iloc[:train_end]
        val_labels = labels.iloc[train_end:val_end]
        test_labels = labels.iloc[val_end:]
        
        print(f"📊 数据分割完成:")
        print(f"   训练集: {len(train_df)} 条 ({train_df.index[0]} 到 {train_df.index[-1]})")
        print(f"   验证集: {len(val_df)} 条 ({val_df.index[0]} 到 {val_df.index[-1]})")
        print(f"   测试集: {len(test_df)} 条 ({test_df.index[0]} 到 {test_df.index[-1]})")
        
        return train_df, val_df, test_df, train_labels, val_labels, test_labels
    
    def save_processed_data(self, data: dict, file_path: str):
        """
        保存预处理后的数据
        
        Args:
            data: 要保存的数据字典
            file_path: 保存路径
        """
        try:
            data['save_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            with open(file_path, 'wb') as f:
                pickle.dump(data, f)
            
            print(f"✅ 预处理数据已保存: {file_path}")
            
        except Exception as e:
            print(f"❌ 保存预处理数据失败: {e}")
            raise
    
    def load_processed_data(self, file_path: str) -> dict:
        """
        加载预处理后的数据
        
        Args:
            file_path: 数据文件路径
            
        Returns:
            数据字典
        """
        try:
            with open(file_path, 'rb') as f:
                data = pickle.load(f)
            
            save_time = data.get('save_time', '未知')
            print(f"✅ 预处理数据加载成功: {file_path}")
            print(f"📅 保存时间: {save_time}")
            
            return data
            
        except Exception as e:
            print(f"❌ 加载预处理数据失败: {e}")
            raise
    
    def format_timestamp(self, timestamp: float) -> str:
        """
        格式化时间戳为北京时间
        
        Args:
            timestamp: Unix时间戳
            
        Returns:
            格式化的时间字符串
        """
        dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
        beijing_dt = dt.astimezone(timezone.utc).replace(tzinfo=None) + pd.Timedelta(hours=8)
        return beijing_dt.strftime('%Y-%m-%d %H:%M:%S')
    
    def check_percentage_result(self, start_price: float, price_history: list, 
                               up_threshold: float = 0.01, 
                               down_threshold: float = 0.01, 
                               max_minutes: int = 120) -> Tuple[Optional[int], int]:
        """
        检查百分比结果
        
        Args:
            start_price: 起始价格
            price_history: 价格历史列表
            up_threshold: 上涨阈值
            down_threshold: 下跌阈值
            max_minutes: 最大等待分钟数
            
        Returns:
            (结果, 实际分钟数) - 结果: 1=先涨1%, 0=先跌1%, None=都没达到
        """
        up_target = start_price * (1 + up_threshold)
        down_target = start_price * (1 - down_threshold)
        
        for i, price in enumerate(price_history[:max_minutes]):
            if price >= up_target:
                return 1, i + 1  # 先涨1%
            elif price <= down_target:
                return 0, i + 1  # 先跌1%
        
        return None, max_minutes  # 都没达到
    
    def validate_data_quality(self, df: pd.DataFrame) -> bool:
        """
        验证数据质量
        
        Args:
            df: 数据框
            
        Returns:
            数据是否有效
        """
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        
        # 检查必需列
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"❌ 缺少必需列: {missing_columns}")
            return False
        
        # 检查数据完整性
        null_counts = df[required_columns].isnull().sum()
        if null_counts.sum() > 0:
            print(f"⚠️  发现空值: {null_counts.to_dict()}")
        
        # 检查价格逻辑
        invalid_prices = (df['high'] < df['low']) | (df['high'] < df['open']) | (df['high'] < df['close']) | \
                        (df['low'] > df['open']) | (df['low'] > df['close'])
        
        if invalid_prices.sum() > 0:
            print(f"⚠️  发现无效价格: {invalid_prices.sum()} 条")
        
        print(f"✅ 数据质量检查完成")
        return True
