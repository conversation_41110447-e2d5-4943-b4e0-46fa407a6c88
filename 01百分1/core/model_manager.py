#!/usr/bin/env python3
"""
模型管理公共模块
统一模型训练、保存、加载、预测等功能
"""

import joblib
import json
import pandas as pd
import numpy as np
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from lightgbm import LGBMClassifier
from typing import Tuple, Optional, Dict, Any
import os

class ModelManager:
    """模型管理器"""

    def __init__(self, model_dir: str = "models"):
        """
        初始化模型管理器

        Args:
            model_dir: 模型保存目录
        """
        self.model_dir = model_dir
        self.model = None
        self.config = {}

        # 确保模型目录存在
        os.makedirs(model_dir, exist_ok=True)

    def train_model(self, X_train: pd.DataFrame, y_train: pd.Series,
                   X_val: pd.DataFrame, y_val: pd.Series,
                   feature_list: list) -> LGBMClassifier:
        """
        训练LightGBM模型

        Args:
            X_train: 训练特征
            y_train: 训练标签
            X_val: 验证特征
            y_val: 验证标签
            feature_list: 特征列表

        Returns:
            训练好的模型
        """
        print("🤖 开始训练LightGBM模型...")

        # 准备训练数据
        X_train_features = X_train[feature_list]
        X_val_features = X_val[feature_list]

        # 模型参数
        model_params = {
            'objective': 'binary',
            'metric': 'binary_logloss',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1,
            'random_state': 42
        }

        # 创建和训练模型
        self.model = LGBMClassifier(**model_params)

        self.model.fit(
            X_train_features, y_train,
            eval_set=[(X_val_features, y_val)],
            eval_names=['validation']
        )

        print("✅ 模型训练完成")

        return self.model

    def evaluate_model(self, X_test: pd.DataFrame, y_test: pd.Series,
                      feature_list: list) -> Dict[str, Any]:
        """
        评估模型性能

        Args:
            X_test: 测试特征
            y_test: 测试标签
            feature_list: 特征列表

        Returns:
            评估结果字典
        """
        if self.model is None:
            raise ValueError("模型未训练或加载")

        print("📊 评估模型性能...")

        X_test_features = X_test[feature_list]

        # 预测
        y_pred = self.model.predict(X_test_features)
        y_pred_proba = self.model.predict_proba(X_test_features)[:, 1]

        # 计算指标
        accuracy = accuracy_score(y_test, y_pred)

        # 寻找最优阈值
        best_threshold, best_accuracy = self._find_best_threshold(y_test, y_pred_proba)

        # 使用最优阈值重新预测
        y_pred_optimal = (y_pred_proba > best_threshold).astype(int)
        optimal_accuracy = accuracy_score(y_test, y_pred_optimal)

        results = {
            'accuracy': accuracy,
            'best_threshold': best_threshold,
            'optimal_accuracy': optimal_accuracy,
            'classification_report': classification_report(y_test, y_pred_optimal),
            'confusion_matrix': confusion_matrix(y_test, y_pred_optimal).tolist()
        }

        print(f"📈 模型性能:")
        print(f"   默认准确率: {accuracy:.4f}")
        print(f"   最优阈值: {best_threshold:.4f}")
        print(f"   最优准确率: {optimal_accuracy:.4f}")

        return results

    def _find_best_threshold(self, y_true: pd.Series, y_pred_proba: np.ndarray) -> Tuple[float, float]:
        """
        寻找最优阈值

        Args:
            y_true: 真实标签
            y_pred_proba: 预测概率

        Returns:
            (最优阈值, 最优准确率)
        """
        thresholds = np.arange(0.1, 0.9, 0.01)
        best_threshold = 0.5
        best_accuracy = 0

        for threshold in thresholds:
            y_pred = (y_pred_proba > threshold).astype(int)
            accuracy = accuracy_score(y_true, y_pred)

            if accuracy > best_accuracy:
                best_accuracy = accuracy
                best_threshold = threshold

        return best_threshold, best_accuracy

    def get_feature_importance(self, feature_list: list) -> pd.DataFrame:
        """
        获取特征重要性

        Args:
            feature_list: 特征列表

        Returns:
            特征重要性DataFrame
        """
        if self.model is None:
            raise ValueError("模型未训练或加载")

        importance_df = pd.DataFrame({
            'feature': feature_list,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)

        return importance_df

    def save_model(self, model_name: str = "btc_percentage_model",
                   config_data: Dict[str, Any] = None):
        """
        保存模型和配置

        Args:
            model_name: 模型名称
            config_data: 配置数据
        """
        if self.model is None:
            raise ValueError("没有模型可保存")

        model_path = os.path.join(self.model_dir, f"{model_name}.joblib")
        config_path = os.path.join(self.model_dir, f"{model_name}_config.json")

        # 保存模型
        joblib.dump(self.model, model_path)

        # 保存配置
        if config_data:
            self.config = config_data
            with open(config_path, 'w') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)

        print(f"✅ 模型已保存:")
        print(f"   模型文件: {model_path}")
        print(f"   配置文件: {config_path}")

    def load_model(self, model_name: str = "btc_percentage_model") -> Tuple[object, Dict[str, Any]]:
        """
        加载模型和配置

        Args:
            model_name: 模型名称

        Returns:
            (模型对象, 配置字典)
        """
        model_path = os.path.join(self.model_dir, f"{model_name}.joblib")
        config_path = os.path.join(self.model_dir, f"{model_name}_config.json")

        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")

        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")

        # 加载模型
        self.model = joblib.load(model_path)

        # 加载配置
        with open(config_path, 'r') as f:
            self.config = json.load(f)

        print(f"✅ 模型加载成功:")
        print(f"   模型类型: {self.config.get('model_type', 'Unknown')}")
        print(f"   最优阈值: {self.config.get('best_threshold', 0.5):.3f}")
        print(f"   特征数量: {len(self.config.get('feature_list', []))}")

        return self.model, self.config

    def predict(self, X: pd.DataFrame, feature_list: list) -> Tuple[Optional[int], float]:
        """
        进行预测

        Args:
            X: 特征数据
            feature_list: 特征列表

        Returns:
            (预测结果, 概率) - 预测结果: 1=先涨1%, 0=先跌1%, None=信心不足
        """
        if self.model is None:
            raise ValueError("模型未加载")

        if len(X) == 0:
            return None, 0.0

        try:
            # 获取最新特征
            latest_features = X[feature_list].iloc[-1:]

            # 预测概率
            probability = self.model.predict_proba(latest_features)[0, 1]

            # 根据阈值决策
            best_threshold = self.config.get('best_threshold', 0.5)

            guess = None
            if probability > best_threshold:
                guess = 1  # 预测先涨1%
            elif probability < (1 - best_threshold):
                guess = 0  # 预测先跌1%

            return guess, probability

        except Exception as e:
            print(f"❌ 预测失败: {e}")
            return None, 0.0

    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息

        Returns:
            模型信息字典
        """
        if not self.config:
            return {}

        return {
            'model_type': self.config.get('model_type', 'Unknown'),
            'best_threshold': self.config.get('best_threshold', 0.5),
            'feature_count': len(self.config.get('feature_list', [])),
            'target_description': self.config.get('target_description', ''),
            'feature_list': self.config.get('feature_list', [])
        }
