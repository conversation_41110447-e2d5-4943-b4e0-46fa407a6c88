import pandas as pd

# 加载数据
df = pd.read_csv('btcusd_1-min_data.csv')

# 将Unix时间戳转换为日期时间格式，并设为索引
# 注意：你的时间戳看起来非常大，可能单位不是秒。需要确认一下。
# 常见的Unix时间戳是10位数（秒）。如果你的时间戳单位是毫秒、微秒或纳秒，需要相应调整。
# 假设是秒：
df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s') 
df.set_index('Timestamp', inplace=True)

# 方法一：使用固定的日期范围
# 计算3年前的日期
from datetime import datetime, timedelta  # 这里修正了拼写错误
three_years_ago = datetime.now() - timedelta(days=300)

# 筛选最近3年的数据
df_recent = df.loc[three_years_ago:]
print(f"原始数据范围: {df.index.min()} 到 {df.index.max()}")
print(f"筛选后数据范围: {df_recent.index.min()} 到 {df_recent.index.max()}")
df = df_recent

# 为了后续计算方便，重命名列（可选，但推荐）
df.rename(columns={
    'Open': 'open',
    'High': 'high',
    'Low': 'low',
    'Close': 'close',
    'Volume': 'volume'
}, inplace=True)

# 检查数据是否有缺失，并按时间排序
df.dropna(inplace=True)
df.sort_index(inplace=True)

df['future_price_10min'] = df['close'].shift(-1)
df['label'] = (df['future_price_10min'] > df['close']).astype(int)
df.dropna(inplace=True)

# --- 1. 价格/动量特征 (基于Close) ---
for n in [1, 3, 5, 10, 30, 60]:
    df[f'return_{n}min'] = df['close'].pct_change(n)

# --- 2. 趋势特征 (基于Close) ---
for n in [10, 30, 60]:
    df[f'sma_{n}'] = df['close'].rolling(window=n).mean()
    df[f'price_div_sma_{n}'] = df['close'] / df[f'sma_{n}']
df['sma_10_div_sma_30'] = df['sma_10'] / df['sma_30']

# --- 3. 成交量特征 (基于Volume) ---
for n in [10, 30, 60]:
    df[f'vma_{n}'] = df['volume'].rolling(window=n).mean()
    # 避免除以零
    df[f'volume_div_vma_{n}'] = df['volume'] / (df[f'vma_{n}'] + 1e-9)

# --- 4. 价量结合特征 (VWAP) ---
df['price_x_volume'] = df['close'] * df['volume']
vwap_numerator = df['price_x_volume'].rolling(window=30).sum()
vwap_denominator = df['volume'].rolling(window=30).sum()
df['vwap_30'] = vwap_numerator / (vwap_denominator + 1e-9)
df['price_div_vwap_30'] = df['close'] / (df['vwap_30'] + 1e-9)
df.drop('price_x_volume', axis=1, inplace=True)

# --- 5. 【旗舰版】多时间尺度、动态K线形态分析 ---

# --- 准备工作 ---
epsilon = 1e-9

# --- 函数1: 计算标准化的K线特征 (可复用) ---
def get_standardized_kline_features(df, timeframe_suffix=''):
    """在一个DataFrame上计算标准化的K线特征"""
    # 1. 计算ATR
    high_low = df['high'] - df['low']
    high_prev_close = abs(df['high'] - df['close'].shift(1))
    low_prev_close = abs(df['low'] - df['close'].shift(1))
    tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)
    atr_col = f'atr_14{timeframe_suffix}'
    df[atr_col] = tr.rolling(window=14).mean()

    # 2. 计算形态的绝对值
    body_size = abs(df['close'] - df['open'])
    price_range = df['high'] - df['low']
    upper_shadow = df['high'] - df[['open', 'close']].max(axis=1)
    lower_shadow = df[['open', 'close']].min(axis=1) - df['low']
    
    # 3. 标准化
    df[f'body_percent_of_range{timeframe_suffix}'] = body_size / (price_range + epsilon)
    df[f'upper_shadow_percent_of_range{timeframe_suffix}'] = upper_shadow / (price_range + epsilon)
    df[f'lower_shadow_percent_of_range{timeframe_suffix}'] = lower_shadow / (price_range + epsilon)
    df[f'range_norm_by_atr{timeframe_suffix}'] = price_range / (df[atr_col] + epsilon)
    
    return df

# --- 函数2: 计算正在形成的K线特征 ---
def add_live_kline_features(df, timeframe):
    """计算"到目前为止"的K线特征"""
    tf_str = f'{timeframe}T'
    suffix = f'_live_{timeframe}m'

    # 确定每个1分钟K线所属的大周期K线的起始时间
    df['start_of_bar'] = df.index.floor(tf_str)
    
    # 按起始时间分组
    grouped = df.groupby('start_of_bar')
    
    # 在每个组内动态计算"到目前为止"的OHLC
    live_open = grouped['open'].transform('first')
    live_high = grouped['high'].transform('cummax') # 累积最大值
    live_low = grouped['low'].transform('cummin')   # 累积最小值
    live_close = df['close'] # close就是当前分钟的close
    
    # 使用这些动态OHLC计算动态的形态特征
    live_body_size = abs(live_close - live_open)
    live_price_range = live_high - live_low
    live_upper_shadow = live_high - pd.concat([live_open, live_close], axis=1).max(axis=1)
    live_lower_shadow = pd.concat([live_open, live_close], axis=1).min(axis=1) - live_low
    
    # 标准化 (这里用1分钟的ATR作为基准，因为大周期的ATR还未形成)
    df[f'body_percent{suffix}'] = live_body_size / (live_price_range + epsilon)
    df[f'upper_shadow_percent{suffix}'] = live_upper_shadow / (live_price_range + epsilon)
    df[f'lower_shadow_percent{suffix}'] = live_lower_shadow / (live_price_range + epsilon)
    df[f'range_norm_by_atr{suffix}'] = live_price_range / (df['atr_14_1m'] + epsilon) # 注意基准
    
    df.drop('start_of_bar', axis=1, inplace=True)
    return df

# --- 执行特征工程 ---

# 1. 计算基础的1分钟K线特征
df = get_standardized_kline_features(df, timeframe_suffix='_1m')

# 2. 计算最近几根1分钟K线的演变趋势 (Rolling)
for window in [3, 5]:
    df[f'body_percent_mean_{window}m'] = df['body_percent_of_range_1m'].rolling(window=window).mean()
    df[f'range_norm_by_atr_mean_{window}m'] = df['range_norm_by_atr_1m'].rolling(window=window).mean()

# 3. 计算已完成的大周期K线特征 (背景)
for tf in [5, 15]:
    df_tf = df.resample(f'{tf}T',label='right', closed='right').agg({'open':'first', 'high':'max', 'low':'min', 'close':'last'})
    df_tf = get_standardized_kline_features(df_tf, timeframe_suffix=f'_{tf}m_completed')
    features_to_merge = [col for col in df_tf.columns if f'_{tf}m_completed' in col]
    df = pd.merge_asof(df, df_tf[features_to_merge], left_index=True, right_index=True, direction='backward')

# 4. 计算正在形成的大周期K线特征 (实时战况)
for tf in [5, 15]:
    df = add_live_kline_features(df, tf)

# 最终清理
df.dropna(inplace=True)
# --- 6. 时间特征 ---
df['hour'] = df.index.hour
df['day_of_week'] = df.index.dayofweek

# --- 最后，清理所有因计算产生的NaN值 ---
df.dropna(inplace=True)

# 定义目标列
target = 'label'

# 定义特征列：排除价格、目标、未来价格以及中间计算的辅助列（如SMA、VMA等）
# 我们只保留那些可以直接用于预测的衍生特征
excluded_columns = ['open', 'high', 'low', 'close', 'volume', 'label', 'future_price_10min']
# 同时排除辅助计算列（可以根据你第三步实际生成的列名进行调整）
excluded_columns += [f'sma_{n}' for n in [10, 30, 60]]
excluded_columns += [f'vma_{n}' for n in [10, 30, 60]]
excluded_columns += ['vwap_30'] 

features = [col for col in df.columns if col not in excluded_columns]

print(f"总共使用的特征数量: {len(features)}")
# print("使用的特征:", features) # 可以打印出来检查一下


# 划分点需要根据你实际数据的日期范围来定。
# 假设我们用前70%做训练，接下来的15%做验证，最后的15%做测试。

train_size = int(len(df) * 0.70)
val_size = int(len(df) * 0.15)

# 训练集 (用于训练模型)
train_df = df.iloc[:train_size]
# 验证集 (用于早停、校准概率、寻找阈值)
val_df = df.iloc[train_size:train_size + val_size]
# 测试集 (用于最终评估，模型训练中不可见)
test_df = df.iloc[train_size + val_size:]
# test_df = df.loc['2025-07-08'] 
X_train, y_train = train_df[features], train_df[target]
X_val, y_val = val_df[features], val_df[target]
X_test, y_test = test_df[features], test_df[target]

print(f"训练集大小: {len(X_train)}, 验证集大小: {len(X_val)}, 测试集大小: {len(X_test)}")

import lightgbm as lgb
# 设置模型参数
lgbm = lgb.LGBMClassifier(objective='binary', 
                          metric='auc', 
                          n_estimators=2000, # 设置一个较大的数，依靠早停来决定最终轮数
                          learning_rate=0.05,
                          n_jobs=-1,
                          random_state=42)

# 训练模型
lgbm.fit(X_train, y_train,
         eval_set=[(X_val, y_val)],
         eval_metric='auc',
         callbacks=[lgb.early_stopping(stopping_rounds=100, verbose=False)])

print("基础模型训练完成。")

from sklearn.calibration import CalibratedClassifierCV

# 使用 Isotonic Regression 进行校准
# cv='prefit' 意味着我们使用已经训练好的 lgbm 模型，只在验证集上训练校准器
calibrated_model = CalibratedClassifierCV(lgbm, method='isotonic', cv='prefit')
calibrated_model.fit(X_val, y_val)

print("模型概率校准完成。")

import numpy as np

# 获取验证集上的校准后概率 (上涨的概率 P(label=1))
val_probabilities = calibrated_model.predict_proba(X_val)[:, 1]

# 设定搜索范围 (理论盈亏平衡点是 0.556)
thresholds_to_test = np.arange(0.56, 0.75, 0.01) 

best_score = -np.inf
best_threshold = 0.5 # 默认值

# 遍历所有阈值
for threshold in thresholds_to_test:
    current_score = 0
    trades_made = 0
    
    # 遍历验证集中的每一个样本
    for i in range(len(val_probabilities)):
        prob = val_probabilities[i]
        actual_result = y_val.iloc[i] # 实际结果 (1=涨, 0=跌)
        
        guess = None
        
        # 决策逻辑
        if prob > threshold:
            guess = 1 # 猜涨
            trades_made += 1
        elif prob < (1 - threshold):
            guess = 0 # 猜跌
            trades_made += 1
        # 否则 (信心不足)，放弃猜测，得分为0
        
        # 计算得分
        if guess is not None:
            if guess == actual_result:
                current_score += 0.8 # 猜对
            else:
                current_score += -1.0 # 猜错
                
    # 打印结果
    # print(f"阈值: {threshold:.2f}, 总得分: {current_score:.2f}, 猜测次数: {trades_made}")
    
    # 更新最优阈值
    if current_score > best_score:
        best_score = current_score
        best_threshold = threshold

print(f"\n验证集上的最优得分: {best_score:.2f}")
print(f"找到的最优信心阈值: {best_threshold:.2f}")

# =================================================================
#      【升级版】测试集详细评估与日志打印
# =================================================================

# 获取测试集上的校准后概率
test_probabilities = calibrated_model.predict_proba(X_test)[:, 1]

# 将概率数组转换为带有时间索引的Pandas Series，方便按时间戳查找
test_prob_series = pd.Series(test_probabilities, index=X_test.index)

# 初始化计分板
test_score = 0
trades_made = 0
wins = 0
losses = 0

# --- 打印表头 ---
print("\n" + "="*80)
print(f"--- 测试集详细评估 (使用阈值: {best_threshold:.2f}) ---")
print("-" * 80)
# 使用格式化字符串，让输出对齐
print(f"{'时间':<22}{'操作':<8}{'信心':<8}{'当前价格':<12}{'十分钟后价格':<15}{'结果':<12}{'得分'}")
print("-" * 80)


# --- 遍历测试集中的每一行来打印详细日志 ---
# 注意：我们遍历 test_df 而不是 X_test，因为 test_df 包含价格信息
for timestamp, row in test_df.iterrows():
    # 确保当前行在我们的预测结果中 (通常都会在)
    if timestamp not in test_prob_series:
        continue

    # --- 提取所有需要的信息 ---
    prob = test_prob_series.loc[timestamp]
    actual_label = row['label']
    current_price = row['close']
    future_price = row['future_price_10min']
    
    # --- 初始化本行的输出变量 ---
    guess = None
    action_str = "放弃"
    result_str = "-"
    score_for_this_trade = 0.0
    best_threshold=0.6
    # --- 决策逻辑 ---
    if prob > best_threshold:
        guess = 1
        action_str = "\033[92m猜涨\033[0m" # 绿色
    elif prob < (1 - best_threshold):
        guess = 0
        action_str = "\033[91m猜跌\033[0m" # 红色
        
    # --- 如果做出了猜测，则进行结果判断和计分 ---
    if guess is not None:
        trades_made += 1
        if guess == actual_label:
            wins += 1
            test_score += 0.8
            score_for_this_trade = 0.8
            result_str = "猜对了✅"
        else:
            losses += 1
            test_score -= 1.0
            score_for_this_trade = -1.0
            result_str = "猜错了❌"
            
    # --- 打印本行的详细结果 ---
    # print(f"{str(timestamp):<22}"
    #       f"{action_str:<15}" # 调整宽度以适应颜色代码
    #       f"{prob:<8.2%}"
    #       f"{current_price:<12.2f}"
    #       f"{future_price:<15.2f}"
    #       f"{result_str:<12}"
    #       f"{score_for_this_trade:+.1f}")


# --- 循环结束后，打印最终的统计摘要 ---
print("-" * 80)
print("\n--- 测试集最终评估结果 ---")
print(f"使用的阈值: {best_threshold:.2f}")
print(f"总样本数: {len(test_df)}")
print(f"猜测次数: {trades_made} (占比: {trades_made/len(test_df)*100:.2f}%)")
if trades_made > 0:
    print(f"胜率 (仅在猜测时): {wins/trades_made*100:.2f}%")
print(f"总得分: {test_score:.2f}")