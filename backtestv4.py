import pandas as pd
import joblib
import json
import numpy as np
import time
import os
import csv
import argparse
from feature import calculate_all_features
import pytz
# ===================================================================
#      这是一个带实时结果验证和计分的 "回测模拟器" V4
#      【V3更新】: 所有时间显示已转换为东8区（北京时间）
#      【V4更新】: 添加限制 - 一次只能进行一次猜测，等结果出来后再进行下一次猜测
# ===================================================================

# -------------------------------------------------------------------
#      第1部分: 特征工程函数 (保持不变)
# -------------------------------------------------------------------
# -------------------------------------------------------------------
#      第2部分: 预测函数 (【已修改】现在会返回决策)
# -------------------------------------------------------------------
def make_prediction_and_print(model, config, features_df):
    """
    接收一个已经计算好特征的DataFrame，执行预测并打印。
    这个函数现在不负责数据加载和特征计算。
    """
    try:
        best_threshold = config['best_threshold']
        feature_list = config['feature_list']

        latest_features = features_df[feature_list].iloc[-1:]
        current_price = features_df.iloc[-1]['close']

        probability = model.predict_proba(latest_features)[0, 1]

        guess = None
        if probability > best_threshold:
            guess = 1
        elif probability < (1 - best_threshold):
            guess = 0

        return guess, probability, current_price

    except Exception as e:
        print(f"预测时发生错误: {e}")
        return None, None, None

# -------------------------------------------------------------------
#      第3部分: 主模拟循环 (【V4升级】一次只能进行一次猜测)
# -------------------------------------------------------------------
def run_backtest_simulation(source_file, start_row, start_time, speed, initial_buffer):
    destination_file = 'live_btc_data.csv'

    # --- 1. 加载工具和初始化状态 ---
    model = joblib.load('btc_prediction_model.joblib')
    with open('model_config.json', 'r') as f:
        config = json.load(f)
    df_source = pd.read_csv(source_file)
    # 转换时间戳为东8区时间
    df_source['Timestamp_dt'] = pd.to_datetime(df_source['Timestamp'], unit='s', utc=True).dt.tz_convert('Asia/Shanghai')
    df_source.set_index('Timestamp_dt', inplace=True, drop=False)
    df_source.sort_index(inplace=True)

    # 初始化计分板和状态
    current_guess = None  # 当前正在等待验证的猜测
    total_score, wins, losses = 0, 0, 0

    print("🔒 V4限制模式：一次只能进行一次猜测，等结果出来后再进行下一次猜测")

    # --- 2. 确定起始点 (逻辑同V2) ---
    actual_start_index = 0
    if start_time:
        try:
            # 将用户输入的时间解析为东8区时间
            user_start_time = pd.to_datetime(start_time)
            # 如果用户输入的时间没有时区信息，假设为东8区时间
            if user_start_time.tz is None:
                user_start_time = user_start_time.tz_localize('Asia/Shanghai')
            else:
                user_start_time = user_start_time.tz_convert('Asia/Shanghai')
            actual_start_index = df_source.index.searchsorted(user_start_time, side='left')
        except Exception as e: # 简化错误处理
            print(f"时间格式错误: {start_time}, 错误信息: {e}")
            return
    else:
        actual_start_index = start_row

    if actual_start_index < initial_buffer:
        actual_start_index = initial_buffer

    # --- 3. 准备初始数据缓冲区 ---
    buffer_start_index = actual_start_index - initial_buffer
    initial_data = df_source.iloc[buffer_start_index:actual_start_index]
    original_header = pd.read_csv(source_file, nrows=0).columns.tolist()
    initial_data[original_header].to_csv(destination_file, index=False)

    print("-" * 50 + f"\n模拟开始于 {df_source.index[actual_start_index]}！按 Ctrl+C 停止。\n" + "-" * 50)

    # --- 4. 主循环（V4版本：一次只能进行一次猜测）---
    try:
        for index_timestamp, row in df_source.iloc[actual_start_index:].iterrows():
            # a. 更新实时数据文件
            with open(destination_file, 'a', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(row[original_header].values)

            current_time = index_timestamp
            current_price = row['Close']
            print(f"数据更新 -> 时间: {current_time}, 价格: {current_price:.2f}")

            # b. 【V4新逻辑】检查是否有当前猜测需要验证
            if current_guess is not None and current_time >= current_guess['resolve_time']:
                guess_price = current_guess['guess_price']
                guess_direction = current_guess['guess_direction']
                guess_time = current_guess['guess_time']

                # 确定实际结果
                actual_direction = 1 if current_price > guess_price else 0

                print("="*18 + " 🚀 结果验证 " + "="*18)
                print(f"验证在 {guess_time} 做出的猜测:")

                # 比较猜测和实际结果
                if guess_direction == actual_direction:
                    total_score += 0.8
                    wins += 1
                    direction_str = "涨" if guess_direction == 1 else "跌"
                    print(f"猜对了✅! 当时猜测【{direction_str}】({guess_price:.2f} -> {current_price:.2f})")
                    print(f"\033[92m得分: +0.8, 当前总分: {total_score:.2f}\033[0m")
                else:
                    total_score -= 1.0
                    losses += 1
                    direction_str = "涨" if guess_direction == 1 else "跌"
                    print(f"猜错了❌! 当时猜测【{direction_str}】({guess_price:.2f} -> {current_price:.2f})")
                    print(f"\033[91m得分: -1.0, 当前总分: {total_score:.2f}\033[0m")

                print("="*52)
                print("🔓 猜测已验证，现在可以进行下一次猜测")
                # 清除当前猜测，允许进行下一次猜测
                current_guess = None

            # c. 【V4新逻辑】只有在没有待验证猜测时才能进行新的预测
            if current_guess is None:
                # 计算特征
                try:
                    live_data_df = pd.read_csv(destination_file)
                    live_data_df['Timestamp'] = pd.to_datetime(live_data_df['Timestamp'], unit='s', utc=True).dt.tz_convert('Asia/Shanghai')
                    live_data_df.set_index('Timestamp', inplace=True)
                    live_data_df.rename(columns={'Open':'open', 'High':'high', 'Low':'low', 'Close':'close', 'Volume':'volume'}, inplace=True)

                    if len(live_data_df) >= 300:  # 确保有足够的历史数据
                        features_df = calculate_all_features(live_data_df.copy())

                        if not features_df.empty:
                            # 执行预测
                            guess, probability, price_at_guess = make_prediction_and_print(model, config, features_df)

                            # 如果做出了猜测，记录下来
                            if guess is not None:
                                resolve_time = current_time + pd.Timedelta(minutes=10)
                                current_guess = {
                                    'guess_price': price_at_guess,
                                    'guess_direction': guess,
                                    'guess_time': current_time,
                                    'resolve_time': resolve_time
                                }
                                direction_str = "涨" if guess == 1 else "跌"
                                print(f"🔒 模型决策: 猜测【{direction_str}】 (信心: {probability:.2f})")
                                print(f"🔒 此猜测将在 {resolve_time} 进行验证")
                                print(f"🔒 在验证完成前不会进行新的猜测\n")
                            else:
                                print("📊 模型分析: 信心不足，放弃本次猜测")

                except Exception as e:
                    print(f"特征计算或预测出错: {e}")
            else:
                # 当前有待验证的猜测，显示等待状态
                remaining_time = current_guess['resolve_time'] - current_time
                if remaining_time.total_seconds() > 0:
                    remaining_minutes = int(remaining_time.total_seconds() / 60)
                    print(f"⏳ 等待验证中... 还需 {remaining_minutes} 分钟")

            time.sleep(speed)

    except KeyboardInterrupt:
        print("\n" + "-" * 50 + "\n模拟被用户停止。")
    finally:
        print("\n" + "="*20 + " 模拟总结 " + "="*20)
        total_trades = wins + losses
        win_rate = (wins / total_trades * 100) if total_trades > 0 else 0
        print(f"总得分: {total_score:.2f}")
        print(f"总猜测次数: {total_trades}")
        print(f"胜利: {wins}, 失败: {losses}")
        print(f"胜率: {win_rate:.2f}%")
        print("="*54)

        if os.path.exists(destination_file):
            os.remove(destination_file)

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="运行一个带实时结果验证的回测模拟器 V4 - 一次只能进行一次猜测。")
    parser.add_argument("source_file", help="源历史数据CSV文件。")
    parser.add_argument("--start-time", type=str, default=None, help="模拟的起始时间（北京时间）。格式: 'YYYY-MM-DD HH:MM:SS'，例如: '2025-07-08 14:30:00'。")
    parser.add_argument("--start-row", type=int, default=300, help="模拟的起始行号 (当 --start-time 未提供时生效)。")
    parser.add_argument("--speed", type=float, default=0.1, help="模拟速度，秒/行 (建议设小一点以快速看到结果)。")

    args = parser.parse_args()
    INITIAL_BUFFER_SIZE = 2000
    run_backtest_simulation(args.source_file, args.start_row, args.start_time, args.speed, INITIAL_BUFFER_SIZE)