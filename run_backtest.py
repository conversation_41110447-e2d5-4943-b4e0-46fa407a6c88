#!/usr/bin/env python3
"""
回测启动器 - 选择V3或V4版本
"""

import subprocess
import sys
import os

def check_requirements():
    """检查必要文件是否存在"""
    required_files = [
        'btcusd_1-min_data.csv',
        'btc_prediction_model.joblib', 
        'model_config.json',
        'backtestv3.py',
        'backtestv4.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n请确保所有文件都存在后再运行。")
        return False
    
    print("✅ 所有必要文件检查通过！")
    return True

def show_version_comparison():
    """显示版本对比"""
    print("\n" + "="*60)
    print("版本选择指南")
    print("="*60)
    
    print("\n🔄 backtestv3.py - 并发模式")
    print("✅ 可以同时进行多个猜测")
    print("✅ 交易频率较高")
    print("✅ 适合快速测试模型效果")
    print("⚠️  风险敞口较大")
    
    print("\n🔒 backtestv4.py - 限制模式")
    print("✅ 一次只能进行一次猜测")
    print("✅ 更符合实际交易环境")
    print("✅ 风险控制更好")
    print("✅ 适合模拟真实交易")
    
    print("\n💡 建议：")
    print("• 新手或风险厌恶者：选择V4")
    print("• 快速测试模型：选择V3")
    print("• 模拟真实交易：选择V4")

def get_user_input():
    """获取用户输入"""
    print("\n" + "="*60)
    print("参数设置")
    print("="*60)
    
    # 选择版本
    print("\n请选择回测版本：")
    print("1. V3 - 并发模式（可同时多个猜测）")
    print("2. V4 - 限制模式（一次只能一个猜测）")
    
    while True:
        version_choice = input("\n请输入选择 (1/2): ").strip()
        if version_choice in ['1', '2']:
            break
        print("❌ 请输入 1 或 2")
    
    script_name = 'backtestv3.py' if version_choice == '1' else 'backtestv4.py'
    version_name = 'V3 (并发模式)' if version_choice == '1' else 'V4 (限制模式)'
    
    # 选择启动方式
    print("\n请选择启动方式：")
    print("1. 使用行号启动（推荐）")
    print("2. 使用北京时间启动")
    
    while True:
        start_choice = input("\n请输入选择 (1/2): ").strip()
        if start_choice in ['1', '2']:
            break
        print("❌ 请输入 1 或 2")
    
    if start_choice == '1':
        # 行号启动
        default_row = 5000
        start_row = input(f"\n请输入起始行号 (默认 {default_row}): ").strip()
        if not start_row:
            start_row = str(default_row)
        
        try:
            start_row = int(start_row)
            if start_row < 300:
                print("⚠️  行号太小，调整为300")
                start_row = 300
        except ValueError:
            print("❌ 无效行号，使用默认值")
            start_row = default_row
        
        start_param = ['--start-row', str(start_row)]
        start_desc = f"第{start_row}行"
        
    else:
        # 时间启动
        print("\n请输入北京时间（格式：YYYY-MM-DD HH:MM:SS）")
        print("例如：2025-07-08 14:30:00")
        
        start_time = input("\n北京时间: ").strip()
        if not start_time:
            print("❌ 时间不能为空，使用行号模式")
            start_param = ['--start-row', '5000']
            start_desc = "第5000行"
        else:
            start_param = ['--start-time', start_time]
            start_desc = f"北京时间 {start_time}"
    
    # 设置速度
    print("\n请设置模拟速度（每行数据间隔秒数）：")
    print("• 0.01 - 极快（用于快速测试）")
    print("• 0.1  - 快速（推荐）")
    print("• 0.5  - 中等")
    print("• 1.0  - 慢速（便于观察）")
    
    speed = input("\n请输入速度 (默认 0.1): ").strip()
    if not speed:
        speed = '0.1'
    
    try:
        speed_val = float(speed)
        if speed_val <= 0:
            print("⚠️  速度必须大于0，使用默认值")
            speed = '0.1'
    except ValueError:
        print("❌ 无效速度，使用默认值")
        speed = '0.1'
    
    return script_name, version_name, start_param, start_desc, speed

def run_backtest(script_name, version_name, start_param, start_desc, speed):
    """运行回测"""
    print("\n" + "="*60)
    print("开始回测")
    print("="*60)
    
    print(f"📊 版本: {version_name}")
    print(f"🚀 起始: {start_desc}")
    print(f"⚡ 速度: {speed} 秒/行")
    print(f"📝 脚本: {script_name}")
    
    print("\n按 Ctrl+C 可以随时停止回测")
    print("-" * 60)
    
    try:
        cmd = [
            sys.executable, script_name,
            'btcusd_1-min_data.csv'
        ] + start_param + ['--speed', speed]
        
        print(f"执行命令: {' '.join(cmd)}")
        print("-" * 60)
        
        subprocess.run(cmd, check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 回测运行出错: {e}")
    except KeyboardInterrupt:
        print(f"\n⏹️  回测被用户中断")
    except FileNotFoundError:
        print(f"\n❌ 找不到脚本文件: {script_name}")

def main():
    """主函数"""
    print("="*60)
    print("BTC量化交易回测启动器")
    print("="*60)
    
    # 检查必要文件
    if not check_requirements():
        return
    
    # 显示版本对比
    show_version_comparison()
    
    # 获取用户输入
    script_name, version_name, start_param, start_desc, speed = get_user_input()
    
    # 确认运行
    print(f"\n即将运行: {version_name}")
    print(f"起始位置: {start_desc}")
    print(f"模拟速度: {speed} 秒/行")
    
    confirm = input("\n确认运行？(y/n): ").lower().strip()
    if confirm in ['y', 'yes']:
        run_backtest(script_name, version_name, start_param, start_desc, speed)
    else:
        print("\n👋 已取消运行")
    
    print("\n" + "="*60)
    print("感谢使用BTC量化交易回测系统！")
    print("="*60)

if __name__ == '__main__':
    main()
