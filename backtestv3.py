import pandas as pd
import joblib
import json
import numpy as np
import time
import os
import csv
import argparse
from feature import calculate_all_features
# ===================================================================
#      这是一个带实时结果验证和计分的 "回测模拟器" V3
# ===================================================================

# -------------------------------------------------------------------
#      第1部分: 特征工程函数 (保持不变)
# -------------------------------------------------------------------
# -------------------------------------------------------------------
#      第2部分: 预测函数 (【已修改】现在会返回决策)
# -------------------------------------------------------------------
def make_prediction(model, config, live_data_file):
    """
    接收加载好的模型和配置，执行一次预测，并返回决策。
    返回: (guess, probability, current_price)
          guess: 1 for 'Up', 0 for 'Down', None for 'Abandon'
    """
    minutes_needed = 300
    try:
        live_data_df = pd.read_csv(live_data_file)
        live_data_df['Timestamp'] = pd.to_datetime(live_data_df['Timestamp'], unit='s')
        live_data_df.set_index('Timestamp', inplace=True)
        live_data_df.rename(columns={'Open':'open', 'High':'high', 'Low':'low', 'Close':'close', 'Volume':'volume'}, inplace=True)

        if len(live_data_df) < minutes_needed:
            return None, None, None

        features_df = calculate_all_features(live_data_df.copy())
        
        if features_df.empty:
            return None, None, None

        best_threshold = config['best_threshold']
        feature_list = config['feature_list']
        latest_features = features_df[feature_list].iloc[-1:]
        current_price = features_df.iloc[-1]['close']
        
        probability = model.predict_proba(latest_features)[0, 1]
        
        guess = None
        if probability > best_threshold:
            guess = 1  # 涨
        elif probability < (1 - best_threshold):
            guess = 0  # 跌
            
        return guess, probability, current_price

    except Exception as e:
        print(f"预测时发生错误: {e}")
        return None, None, None

# -------------------------------------------------------------------
#      第3部分: 主模拟循环 (【已升级】带结果验证和计分)
# -------------------------------------------------------------------
def run_backtest_simulation(source_file, start_row, start_time, speed, initial_buffer):
    destination_file = 'live_btc_data.csv'
    
    # --- 1. 加载工具和初始化状态 ---
    model = joblib.load('btc_prediction_model.joblib')
    with open('model_config.json', 'r') as f:
        config = json.load(f)
    df_source = pd.read_csv(source_file)
    df_source['Timestamp_dt'] = pd.to_datetime(df_source['Timestamp'], unit='s')
    df_source.set_index('Timestamp_dt', inplace=True, drop=False)
    df_source.sort_index(inplace=True)
    
    # 初始化计分板和待验证的猜测池
    pending_guesses = {} # key: resolve_time, value: {guess_price, guess_direction}
    total_score, wins, losses = 0, 0, 0

    # --- 2. 确定起始点 (逻辑同V2) ---
    actual_start_index = 0
    if start_time:
        try:
            user_start_time = pd.to_datetime(start_time)
            actual_start_index = df_source.index.searchsorted(user_start_time, side='left')
        except Exception: # 简化错误处理
            print(f"时间格式错误: {start_time}")
            return
    else:
        actual_start_index = start_row
    
    if actual_start_index < initial_buffer:
        actual_start_index = initial_buffer
    
    # --- 3. 准备初始数据缓冲区 ---
    buffer_start_index = actual_start_index - initial_buffer
    initial_data = df_source.iloc[buffer_start_index:actual_start_index]
    original_header = pd.read_csv(source_file, nrows=0).columns.tolist()
    initial_data[original_header].to_csv(destination_file, index=False)

    print("-" * 50 + f"\n模拟开始于 {df_source.index[actual_start_index]}！按 Ctrl+C 停止。\n" + "-" * 50)

    # --- 4. 主循环 ---
    try:
        for index_timestamp, row in df_source.iloc[actual_start_index:].iterrows():
            # a. 更新实时数据文件
            with open(destination_file, 'a', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(row[original_header].values)
            
            current_time = index_timestamp
            current_price = row['Close']
            print(f"数据更新 -> 时间: {current_time}, 价格: {current_price:.2f}")

            # b. 【新】检查是否有到期的猜测需要验证
            if current_time in pending_guesses:
                guess_info = pending_guesses[current_time]
                guess_price = guess_info['guess_price']
                guess_direction = guess_info['guess_direction'] # 1=涨, 0=跌
                guess_time = guess_info['guess_time']
                
                # 确定实际结果
                actual_direction = 1 if current_price > guess_price else 0
                
                print("="*18 + " 🚀 结果验证 " + "="*18)
                print(f"验证在 {guess_time} 做出的猜测:")
                
                # 比较猜测和实际结果
                if guess_direction == actual_direction:
                    total_score += 0.8
                    wins += 1
                    direction_str = "涨" if guess_direction == 1 else "跌"
                    print(f"猜对了✅! 当时猜测【{direction_str}】({guess_price:.2f} -> {current_price:.2f})")
                    print(f"\033[92m得分: +0.8, 当前总分: {total_score:.2f}\033[0m")
                else:
                    total_score -= 1.0
                    losses += 1
                    direction_str = "涨" if guess_direction == 1 else "跌"
                    print(f"猜错了❌! 当时猜测【{direction_str}】({guess_price:.2f} -> {current_price:.2f})")
                    print(f"\033[91m得分: -1.0, 当前总分: {total_score:.2f}\033[0m")
                
                print("="*52)
                # 从池中移除已验证的猜测
                del pending_guesses[current_time]

            # c. 执行新的预测
            guess, probability, price_at_guess = make_prediction(model, config, destination_file)
            
            # d. 【新】如果做出了猜测，记录下来
            if guess is not None:
                resolve_time = current_time + pd.Timedelta(minutes=10)
                pending_guesses[resolve_time] = {
                    'guess_price': price_at_guess,
                    'guess_direction': guess,
                    'guess_time': current_time
                }
                direction_str = "涨" if guess == 1 else "跌"
                print(f"--- 模型决策: 猜测【{direction_str}】 (信心: {probability:.2f}) ---")
                print(f"--- 此猜测将在 {resolve_time} 进行验证 ---\n")
            
            time.sleep(speed)
            
    except KeyboardInterrupt:
        print("\n" + "-" * 50 + "\n模拟被用户停止。")
    finally:
        print("\n" + "="*20 + " 模拟总结 " + "="*20)
        total_trades = wins + losses
        win_rate = (wins / total_trades * 100) if total_trades > 0 else 0
        print(f"总得分: {total_score:.2f}")
        print(f"总猜测次数: {total_trades}")
        print(f"胜利: {wins}, 失败: {losses}")
        print(f"胜率: {win_rate:.2f}%")
        print("="*54)

        if os.path.exists(destination_file):
            os.remove(destination_file)

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="运行一个带实时结果验证的回测模拟器。")
    parser.add_argument("source_file", help="源历史数据CSV文件。")
    parser.add_argument("--start-time", type=str, default=None, help="模拟的起始时间。格式: 'YYYY-MM-DD HH:MM:SS'。")
    parser.add_argument("--start-row", type=int, default=300, help="模拟的起始行号 (当 --start-time 未提供时生效)。")
    parser.add_argument("--speed", type=float, default=0.1, help="模拟速度，秒/行 (建议设小一点以快速看到结果)。")
    
    args = parser.parse_args()
    INITIAL_BUFFER_SIZE = 2000 
    run_backtest_simulation(args.source_file, args.start_row, args.start_time, args.speed, INITIAL_BUFFER_SIZE)