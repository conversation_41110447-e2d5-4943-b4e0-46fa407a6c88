{"cells": [{"cell_type": "code", "execution_count": 79, "id": "1fc51cd6", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# 加载数据\n", "df = pd.read_csv('btcusd_1-min_data.csv')\n", "\n", "# 将Unix时间戳转换为日期时间格式，并设为索引\n", "# 注意：你的时间戳看起来非常大，可能单位不是秒。需要确认一下。\n", "# 常见的Unix时间戳是10位数（秒）。如果你的时间戳单位是毫秒、微秒或纳秒，需要相应调整。\n", "# 假设是秒：\n", "df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s') \n", "df.set_index('Timestamp', inplace=True)"]}, {"cell_type": "code", "execution_count": 80, "id": "2073513b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原始数据范围: 2012-01-01 10:01:00 到 2025-07-09 00:49:00\n", "筛选后数据范围: 2024-07-10 09:53:00 到 2025-07-09 00:49:00\n"]}], "source": ["# 方法一：使用固定的日期范围\n", "# 计算3年前的日期\n", "from datetime import datetime, timedelta  # 这里修正了拼写错误\n", "three_years_ago = datetime.now() - <PERSON><PERSON><PERSON>(days=365)\n", "\n", "# 筛选最近3年的数据\n", "df_recent = df.loc[three_years_ago:]\n", "print(f\"原始数据范围: {df.index.min()} 到 {df.index.max()}\")\n", "print(f\"筛选后数据范围: {df_recent.index.min()} 到 {df_recent.index.max()}\")\n", "df = df_recent"]}, {"cell_type": "code", "execution_count": 81, "id": "32812c06", "metadata": {}, "outputs": [], "source": ["# 为了后续计算方便，重命名列（可选，但推荐）\n", "df.rename(columns={\n", "    'Open': 'open',\n", "    'High': 'high',\n", "    'Low': 'low',\n", "    'Close': 'close',\n", "    'Volume': 'volume'\n", "}, inplace=True)\n", "\n", "# 检查数据是否有缺失，并按时间排序\n", "df.dropna(inplace=True)\n", "df.sort_index(inplace=True)\n", "\n", "df['future_price_10min'] = df['close'].shift(-10)\n", "df['label'] = (df['future_price_10min'] > df['close']).astype(int)\n", "df.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": 82, "id": "1aae9826", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/7k/mcrgjq_x5l7bjfqb_bszt78w0000gn/T/ipykernel_39701/2290190091.py:99: FutureWarning: 'T' is deprecated and will be removed in a future version, please use 'min' instead.\n", "  df_tf = df.resample(f'{tf}T').agg({'open':'first', 'high':'max', 'low':'min', 'close':'last'})\n", "/var/folders/7k/mcrgjq_x5l7bjfqb_bszt78w0000gn/T/ipykernel_39701/2290190091.py:99: FutureWarning: 'T' is deprecated and will be removed in a future version, please use 'min' instead.\n", "  df_tf = df.resample(f'{tf}T').agg({'open':'first', 'high':'max', 'low':'min', 'close':'last'})\n", "/var/folders/7k/mcrgjq_x5l7bjfqb_bszt78w0000gn/T/ipykernel_39701/2290190091.py:61: FutureWarning: 'T' is deprecated and will be removed in a future version, please use 'min' instead.\n", "  df['start_of_bar'] = df.index.floor(tf_str)\n", "/var/folders/7k/mcrgjq_x5l7bjfqb_bszt78w0000gn/T/ipykernel_39701/2290190091.py:61: FutureWarning: 'T' is deprecated and will be removed in a future version, please use 'min' instead.\n", "  df['start_of_bar'] = df.index.floor(tf_str)\n"]}], "source": ["# --- 1. 价格/动量特征 (基于Close) ---\n", "for n in [1, 3, 5, 10, 30, 60]:\n", "    df[f'return_{n}min'] = df['close'].pct_change(n)\n", "\n", "# --- 2. 趋势特征 (基于Close) ---\n", "for n in [10, 30, 60]:\n", "    df[f'sma_{n}'] = df['close'].rolling(window=n).mean()\n", "    df[f'price_div_sma_{n}'] = df['close'] / df[f'sma_{n}']\n", "df['sma_10_div_sma_30'] = df['sma_10'] / df['sma_30']\n", "\n", "# --- 3. 成交量特征 (基于Volume) ---\n", "for n in [10, 30, 60]:\n", "    df[f'vma_{n}'] = df['volume'].rolling(window=n).mean()\n", "    # 避免除以零\n", "    df[f'volume_div_vma_{n}'] = df['volume'] / (df[f'vma_{n}'] + 1e-9)\n", "\n", "# --- 4. 价量结合特征 (VWAP) ---\n", "df['price_x_volume'] = df['close'] * df['volume']\n", "vwap_numerator = df['price_x_volume'].rolling(window=30).sum()\n", "vwap_denominator = df['volume'].rolling(window=30).sum()\n", "df['vwap_30'] = vwap_numerator / (vwap_denominator + 1e-9)\n", "df['price_div_vwap_30'] = df['close'] / (df['vwap_30'] + 1e-9)\n", "df.drop('price_x_volume', axis=1, inplace=True)\n", "\n", "# --- 5. 【旗舰版】多时间尺度、动态K线形态分析 ---\n", "\n", "# --- 准备工作 ---\n", "epsilon = 1e-9\n", "\n", "# --- 函数1: 计算标准化的K线特征 (可复用) ---\n", "def get_standardized_kline_features(df, timeframe_suffix=''):\n", "    \"\"\"在一个DataFrame上计算标准化的K线特征\"\"\"\n", "    # 1. 计算ATR\n", "    high_low = df['high'] - df['low']\n", "    high_prev_close = abs(df['high'] - df['close'].shift(1))\n", "    low_prev_close = abs(df['low'] - df['close'].shift(1))\n", "    tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)\n", "    atr_col = f'atr_14{timeframe_suffix}'\n", "    df[atr_col] = tr.rolling(window=14).mean()\n", "\n", "    # 2. 计算形态的绝对值\n", "    body_size = abs(df['close'] - df['open'])\n", "    price_range = df['high'] - df['low']\n", "    upper_shadow = df['high'] - df[['open', 'close']].max(axis=1)\n", "    lower_shadow = df[['open', 'close']].min(axis=1) - df['low']\n", "    \n", "    # 3. 标准化\n", "    df[f'body_percent_of_range{timeframe_suffix}'] = body_size / (price_range + epsilon)\n", "    df[f'upper_shadow_percent_of_range{timeframe_suffix}'] = upper_shadow / (price_range + epsilon)\n", "    df[f'range_norm_by_atr{timeframe_suffix}'] = price_range / (df[atr_col] + epsilon)\n", "    \n", "    return df\n", "\n", "# --- 函数2: 计算正在形成的K线特征 ---\n", "def add_live_kline_features(df, timeframe):\n", "    \"\"\"计算\"到目前为止\"的K线特征\"\"\"\n", "    tf_str = f'{timeframe}T'\n", "    suffix = f'_live_{timeframe}m'\n", "\n", "    # 确定每个1分钟K线所属的大周期K线的起始时间\n", "    df['start_of_bar'] = df.index.floor(tf_str)\n", "    \n", "    # 按起始时间分组\n", "    grouped = df.groupby('start_of_bar')\n", "    \n", "    # 在每个组内动态计算\"到目前为止\"的OHLC\n", "    live_open = grouped['open'].transform('first')\n", "    live_high = grouped['high'].transform('cummax') # 累积最大值\n", "    live_low = grouped['low'].transform('cummin')   # 累积最小值\n", "    live_close = df['close'] # close就是当前分钟的close\n", "    \n", "    # 使用这些动态OHLC计算动态的形态特征\n", "    live_body_size = abs(live_close - live_open)\n", "    live_price_range = live_high - live_low\n", "    live_upper_shadow = live_high - pd.concat([live_open, live_close], axis=1).max(axis=1)\n", "    live_lower_shadow = pd.concat([live_open, live_close], axis=1).min(axis=1) - live_low\n", "    \n", "    # 标准化 (这里用1分钟的ATR作为基准，因为大周期的ATR还未形成)\n", "    df[f'body_percent{suffix}'] = live_body_size / (live_price_range + epsilon)\n", "    df[f'upper_shadow_percent{suffix}'] = live_upper_shadow / (live_price_range + epsilon)\n", "    df[f'lower_shadow_percent{suffix}'] = live_lower_shadow / (live_price_range + epsilon)\n", "    df[f'range_norm_by_atr{suffix}'] = live_price_range / (df['atr_14_1m'] + epsilon) # 注意基准\n", "    \n", "    df.drop('start_of_bar', axis=1, inplace=True)\n", "    return df\n", "\n", "# --- 执行特征工程 ---\n", "\n", "# 1. 计算基础的1分钟K线特征\n", "df = get_standardized_kline_features(df, timeframe_suffix='_1m')\n", "\n", "# 2. 计算最近几根1分钟K线的演变趋势 (Rolling)\n", "for window in [3, 5]:\n", "    df[f'body_percent_mean_{window}m'] = df['body_percent_of_range_1m'].rolling(window=window).mean()\n", "    df[f'range_norm_by_atr_mean_{window}m'] = df['range_norm_by_atr_1m'].rolling(window=window).mean()\n", "\n", "# 3. 计算已完成的大周期K线特征 (背景)\n", "for tf in [5, 15]:\n", "    df_tf = df.resample(f'{tf}T').agg({'open':'first', 'high':'max', 'low':'min', 'close':'last'})\n", "    df_tf = get_standardized_kline_features(df_tf, timeframe_suffix=f'_{tf}m_completed')\n", "    features_to_merge = [col for col in df_tf.columns if f'_{tf}m_completed' in col]\n", "    df = pd.merge_asof(df, df_tf[features_to_merge], left_index=True, right_index=True, direction='backward')\n", "\n", "# 4. 计算正在形成的大周期K线特征 (实时战况)\n", "for tf in [5, 15]:\n", "    df = add_live_kline_features(df, tf)\n", "\n", "# 最终清理\n", "df.dropna(inplace=True)\n", "# --- 6. 时间特征 ---\n", "df['hour'] = df.index.hour\n", "df['day_of_week'] = df.index.dayofweek\n", "\n", "# --- 最后，清理所有因计算产生的NaN值 ---\n", "df.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": 83, "id": "b24e111c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                        open     high      low    close   volume  \\\n", "Timestamp                                                          \n", "2024-07-10 13:00:00  58756.0  58756.0  58726.0  58726.0  0.05162   \n", "\n", "                     future_price_10min  label  return_1min  return_3min  \\\n", "Timestamp                                                                  \n", "2024-07-10 13:00:00             58730.0      1    -0.000511     -0.00034   \n", "\n", "                     return_5min  ...  body_percent_live_5m  \\\n", "Timestamp                         ...                         \n", "2024-07-10 13:00:00     0.000869  ...                   1.0   \n", "\n", "                     upper_shadow_percent_live_5m  \\\n", "Timestamp                                           \n", "2024-07-10 13:00:00                           0.0   \n", "\n", "                     lower_shadow_percent_live_5m  range_norm_by_atr_live_5m  \\\n", "Timestamp                                                                      \n", "2024-07-10 13:00:00                           0.0                   1.308411   \n", "\n", "                     body_percent_live_15m  upper_shadow_percent_live_15m  \\\n", "Timestamp                                                                   \n", "2024-07-10 13:00:00                    1.0                            0.0   \n", "\n", "                     lower_shadow_percent_live_15m  \\\n", "Timestamp                                            \n", "2024-07-10 13:00:00                            0.0   \n", "\n", "                     range_norm_by_atr_live_15m  hour  day_of_week  \n", "Timestamp                                                           \n", "2024-07-10 13:00:00                    1.308411    13            2  \n", "\n", "[1 rows x 54 columns]\n"]}], "source": ["print(df.head(1))"]}, {"cell_type": "code", "execution_count": 84, "id": "4f78a840", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["总共使用的特征数量: 40\n"]}], "source": ["# 定义目标列\n", "target = 'label'\n", "\n", "# 定义特征列：排除价格、目标、未来价格以及中间计算的辅助列（如SMA、VMA等）\n", "# 我们只保留那些可以直接用于预测的衍生特征\n", "excluded_columns = ['open', 'high', 'low', 'close', 'volume', 'label', 'future_price_10min']\n", "# 同时排除辅助计算列（可以根据你第三步实际生成的列名进行调整）\n", "excluded_columns += [f'sma_{n}' for n in [10, 30, 60]]\n", "excluded_columns += [f'vma_{n}' for n in [10, 30, 60]]\n", "excluded_columns += ['vwap_30'] \n", "\n", "features = [col for col in df.columns if col not in excluded_columns]\n", "\n", "print(f\"总共使用的特征数量: {len(features)}\")\n", "# print(\"使用的特征:\", features) # 可以打印出来检查一下"]}, {"cell_type": "code", "execution_count": 85, "id": "20a578db", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['return_1min', 'return_3min', 'return_5min', 'return_10min', 'return_30min', 'return_60min', 'price_div_sma_10', 'price_div_sma_30', 'price_div_sma_60', 'sma_10_div_sma_30', 'volume_div_vma_10', 'volume_div_vma_30', 'volume_div_vma_60', 'price_div_vwap_30', 'atr_14_1m', 'body_percent_of_range_1m', 'upper_shadow_percent_of_range_1m', 'range_norm_by_atr_1m', 'body_percent_mean_3m', 'range_norm_by_atr_mean_3m', 'body_percent_mean_5m', 'range_norm_by_atr_mean_5m', 'atr_14_5m_completed', 'body_percent_of_range_5m_completed', 'upper_shadow_percent_of_range_5m_completed', 'range_norm_by_atr_5m_completed', 'atr_14_15m_completed', 'body_percent_of_range_15m_completed', 'upper_shadow_percent_of_range_15m_completed', 'range_norm_by_atr_15m_completed', 'body_percent_live_5m', 'upper_shadow_percent_live_5m', 'lower_shadow_percent_live_5m', 'range_norm_by_atr_live_5m', 'body_percent_live_15m', 'upper_shadow_percent_live_15m', 'lower_shadow_percent_live_15m', 'range_norm_by_atr_live_15m', 'hour', 'day_of_week']\n"]}], "source": ["print(features)"]}, {"cell_type": "code", "execution_count": 86, "id": "cdcbcadf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集大小: 365449, 验证集大小: 78310, 测试集大小: 78312\n"]}], "source": ["# 划分点需要根据你实际数据的日期范围来定。\n", "# 假设我们用前70%做训练，接下来的15%做验证，最后的15%做测试。\n", "\n", "train_size = int(len(df) * 0.70)\n", "val_size = int(len(df) * 0.15)\n", "\n", "# 训练集 (用于训练模型)\n", "train_df = df.iloc[:train_size]\n", "# 验证集 (用于早停、校准概率、寻找阈值)\n", "val_df = df.iloc[train_size:train_size + val_size]\n", "# 测试集 (用于最终评估，模型训练中不可见)\n", "test_df = df.iloc[train_size + val_size:]\n", "\n", "X_train, y_train = train_df[features], train_df[target]\n", "X_val, y_val = val_df[features], val_df[target]\n", "X_test, y_test = test_df[features], test_df[target]\n", "\n", "print(f\"训练集大小: {len(X_train)}, 验证集大小: {len(X_val)}, 测试集大小: {len(X_test)}\")"]}, {"cell_type": "code", "execution_count": 87, "id": "cdf00453", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[LightGBM] [Info] Number of positive: 182262, number of negative: 183187\n", "[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.005039 seconds.\n", "You can set `force_row_wise=true` to remove the overhead.\n", "And if memory is not enough, you can set `force_col_wise=true`.\n", "[LightGBM] [Info] Total Bins 9721\n", "[LightGBM] [Info] Number of data points in the train set: 365449, number of used features: 40\n", "[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.498734 -> initscore=-0.005062\n", "[LightGBM] [Info] Start training from score -0.005062\n", "基础模型训练完成。\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/project/ai/vibe/daily/btc-quant/.venv/lib/python3.12/site-packages/sklearn/calibration.py:330: FutureWarning: The `cv='prefit'` option is deprecated in 1.6 and will be removed in 1.8. You can use CalibratedClassifierCV(FrozenEstimator(estimator)) instead.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["模型概率校准完成。\n"]}], "source": ["import lightgbm as lgb\n", "# 设置模型参数\n", "lgbm = lgb.LGBMClassifier(objective='binary', \n", "                          metric='auc', \n", "                          n_estimators=2000, # 设置一个较大的数，依靠早停来决定最终轮数\n", "                          learning_rate=0.05,\n", "                          n_jobs=-1,\n", "                          random_state=42)\n", "\n", "# 训练模型\n", "lgbm.fit(X_train, y_train,\n", "         eval_set=[(X_val, y_val)],\n", "         eval_metric='auc',\n", "         callbacks=[lgb.early_stopping(stopping_rounds=100, verbose=False)])\n", "\n", "print(\"基础模型训练完成。\")\n", "\n", "from sklearn.calibration import CalibratedClassifierCV\n", "\n", "# 使用 Isotonic Regression 进行校准\n", "# cv='prefit' 意味着我们使用已经训练好的 lgbm 模型，只在验证集上训练校准器\n", "calibrated_model = CalibratedClassifierCV(lgbm, method='isotonic', cv='prefit')\n", "calibrated_model.fit(X_val, y_val)\n", "\n", "print(\"模型概率校准完成。\")"]}, {"cell_type": "code", "execution_count": 88, "id": "c7b55065", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "验证集上的最优得分: 22560.80\n", "找到的最优信心阈值: 0.56\n"]}], "source": ["import numpy as np\n", "\n", "# 获取验证集上的校准后概率 (上涨的概率 P(label=1))\n", "val_probabilities = calibrated_model.predict_proba(X_val)[:, 1]\n", "\n", "# 设定搜索范围 (理论盈亏平衡点是 0.556)\n", "thresholds_to_test = np.arange(0.56, 0.75, 0.01) \n", "\n", "best_score = -np.inf\n", "best_threshold = 0.5 # 默认值\n", "\n", "# 遍历所有阈值\n", "for threshold in thresholds_to_test:\n", "    current_score = 0\n", "    trades_made = 0\n", "    \n", "    # 遍历验证集中的每一个样本\n", "    for i in range(len(val_probabilities)):\n", "        prob = val_probabilities[i]\n", "        actual_result = y_val.iloc[i] # 实际结果 (1=涨, 0=跌)\n", "        \n", "        guess = None\n", "        \n", "        # 决策逻辑\n", "        if prob > threshold:\n", "            guess = 1 # 猜涨\n", "            trades_made += 1\n", "        elif prob < (1 - threshold):\n", "            guess = 0 # 猜跌\n", "            trades_made += 1\n", "        # 否则 (信心不足)，放弃猜测，得分为0\n", "        \n", "        # 计算得分\n", "        if guess is not None:\n", "            if guess == actual_result:\n", "                current_score += 0.8 # 猜对\n", "            else:\n", "                current_score += -1.0 # 猜错\n", "                \n", "    # 打印结果\n", "    # print(f\"阈值: {threshold:.2f}, 总得分: {current_score:.2f}, 猜测次数: {trades_made}\")\n", "    \n", "    # 更新最优阈值\n", "    if current_score > best_score:\n", "        best_score = current_score\n", "        best_threshold = threshold\n", "\n", "print(f\"\\n验证集上的最优得分: {best_score:.2f}\")\n", "print(f\"找到的最优信心阈值: {best_threshold:.2f}\")"]}, {"cell_type": "code", "execution_count": 93, "id": "cde87c7c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- 测试集最终评估结果 ---\n", "使用的阈值: 0.80\n", "总样本数: 78312\n", "猜测次数: 23548 (占比: 30.07%)\n", "胜率 (仅在猜测时): 88.87%\n", "总得分: 14120.60\n"]}], "source": ["# 获取测试集上的校准后概率\n", "test_probabilities = calibrated_model.predict_proba(X_test)[:, 1]\n", "\n", "test_score = 0\n", "trades_made = 0\n", "wins = 0\n", "losses = 0\n", "best_threshold = 0.8\n", "# 使用最优阈值在测试集上模拟游戏\n", "for i in range(len(test_probabilities)):\n", "    prob = test_probabilities[i]\n", "    actual_result = y_test.iloc[i]\n", "    \n", "    guess = None\n", "    \n", "    if prob > best_threshold:\n", "        guess = 1\n", "    elif prob < (1 - best_threshold):\n", "        guess = 0\n", "        \n", "    if guess is not None:\n", "        trades_made += 1\n", "        if guess == actual_result:\n", "            test_score += 0.8\n", "            wins += 1\n", "        else:\n", "            test_score += -1.0\n", "            losses += 1\n", "\n", "print(\"\\n--- 测试集最终评估结果 ---\")\n", "print(f\"使用的阈值: {best_threshold:.2f}\")\n", "print(f\"总样本数: {len(X_test)}\")\n", "print(f\"猜测次数: {trades_made} (占比: {trades_made/len(X_test)*100:.2f}%)\")\n", "if trades_made > 0:\n", "    print(f\"胜率 (仅在猜测时): {wins/trades_made*100:.2f}%\")\n", "print(f\"总得分: {test_score:.2f}\")"]}, {"cell_type": "code", "execution_count": 94, "id": "3e6f6e33", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型已保存到 btc_prediction_model.joblib\n", "配置已保存到 model_config.json\n"]}], "source": ["import joblib\n", "import json\n", "\n", "# 假设你的这些变量已经准备好\n", "# calibrated_model: 你训练好的校准器\n", "# best_threshold: 你找到的最优信心阈值\n", "# features: 你的特征列表\n", "\n", "# 1. 保存校准后的模型\n", "# joblib是保存scikit-learn模型的标准方法\n", "joblib.dump(calibrated_model, 'btc_prediction_model.joblib')\n", "print(\"模型已保存到 btc_prediction_model.joblib\")\n", "\n", "# 2. 保存最优阈值和特征列表\n", "# 我们用一个JSON文件来保存这些配置信息，清晰明了\n", "config = {\n", "    'best_threshold': best_threshold,\n", "    'feature_list': features # 保存特征列表至关重要，保证实时预测时顺序和内容一致\n", "}\n", "\n", "with open('model_config.json', 'w') as f:\n", "    json.dump(config, f, indent=4)\n", "print(\"配置已保存到 model_config.json\")"]}], "metadata": {"kernelspec": {"display_name": "btc-quant", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}