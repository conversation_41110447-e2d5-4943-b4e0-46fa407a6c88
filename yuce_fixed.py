import pandas as pd
import joblib
import json
import numpy as np

print("实时预测脚本 - 已修复数据泄露问题")

# ===================================================================
#      第1部分: 无前瞻偏差的特征计算函数
# ===================================================================

def calculate_features_no_lookahead_single(df, epsilon=1e-9):
    """
    为实时预测计算特征，严格避免前瞻偏差
    只使用当前时间点及之前的数据
    """
    df_work = df.copy()
    
    # 1. 价格/动量特征
    for n in [1, 3, 5, 10, 30, 60]:
        df_work[f'return_{n}min'] = df_work['close'].pct_change(n)
    
    # 2. 趋势特征
    for n in [10, 30, 60]:
        df_work[f'sma_{n}'] = df_work['close'].rolling(window=n).mean()
        df_work[f'price_div_sma_{n}'] = df_work['close'] / (df_work[f'sma_{n}'] + epsilon)
    df_work['sma_10_div_sma_30'] = df_work['sma_10'] / (df_work['sma_30'] + epsilon)
    
    # 3. 成交量特征
    for n in [10, 30, 60]:
        df_work[f'vma_{n}'] = df_work['volume'].rolling(window=n).mean()
        df_work[f'volume_div_vma_{n}'] = df_work['volume'] / (df_work[f'vma_{n}'] + epsilon)
    
    # 4. VWAP特征
    df_work['price_x_volume'] = df_work['close'] * df_work['volume']
    vwap_numerator = df_work['price_x_volume'].rolling(window=30).sum()
    vwap_denominator = df_work['volume'].rolling(window=30).sum()
    df_work['vwap_30'] = vwap_numerator / (vwap_denominator + epsilon)
    df_work['price_div_vwap_30'] = df_work['close'] / (df_work['vwap_30'] + epsilon)
    df_work.drop('price_x_volume', axis=1, inplace=True)
    
    # 5. 时间特征
    df_work['hour'] = df_work.index.hour
    df_work['day_of_week'] = df_work.index.dayofweek
    
    # 6. K线形态特征
    df_work = get_standardized_kline_features_safe(df_work, timeframe_suffix='_1m', epsilon=epsilon)
    
    # 7. 滚动K线特征
    for window in [3, 5]:
        if f'body_percent_of_range_1m' in df_work.columns:
            df_work[f'body_percent_mean_{window}m'] = df_work['body_percent_of_range_1m'].rolling(window=window).mean()
        if f'range_norm_by_atr_1m' in df_work.columns:
            df_work[f'range_norm_by_atr_mean_{window}m'] = df_work['range_norm_by_atr_1m'].rolling(window=window).mean()
    
    return df_work

def get_standardized_kline_features_safe(df, timeframe_suffix='', epsilon=1e-9):
    """
    安全的K线特征计算函数，添加错误处理
    """
    try:
        # 1. 计算ATR
        high_low = df['high'] - df['low']
        high_prev_close = abs(df['high'] - df['close'].shift(1))
        low_prev_close = abs(df['low'] - df['close'].shift(1))
        tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)
        atr_col = f'atr_14{timeframe_suffix}'
        df[atr_col] = tr.rolling(window=14).mean()

        # 2. 计算形态的绝对值
        body_size = abs(df['close'] - df['open'])
        price_range = df['high'] - df['low']
        upper_shadow = df['high'] - df[['open', 'close']].max(axis=1)
        lower_shadow = df[['open', 'close']].min(axis=1) - df['low']
        
        # 3. 标准化
        df[f'body_percent_of_range{timeframe_suffix}'] = body_size / (price_range + epsilon)
        df[f'upper_shadow_percent_of_range{timeframe_suffix}'] = upper_shadow / (price_range + epsilon)
        df[f'lower_shadow_percent_of_range{timeframe_suffix}'] = lower_shadow / (price_range + epsilon)
        df[f'range_norm_by_atr{timeframe_suffix}'] = price_range / (df[atr_col] + epsilon)
        
    except Exception as e:
        print(f"K线特征计算出错: {e}")
    
    return df

# ===================================================================
#      第2部分: 数据获取函数
# ===================================================================

def get_latest_data(minutes_needed=300):
    """
    获取最新的K线数据
    """
    print("正在获取最新K线数据...")
    
    try:
        # 从实时数据文件读取
        live_data_df = pd.read_csv('live_btc_data.csv') 
    except FileNotFoundError:
        print("错误: 找不到数据文件 'live_btc_data.csv'。")
        print("请确保你有一个实时更新的数据文件，或者修改 get_latest_data 函数来从API获取数据。")
        return None

    # 数据预处理（和训练时保持一致）
    live_data_df['Timestamp'] = pd.to_datetime(live_data_df['Timestamp'], unit='s')
    live_data_df.set_index('Timestamp', inplace=True)
    live_data_df.rename(columns={
        'Open':'open', 'High':'high', 'Low':'low', 'Close':'close', 'Volume':'volume'
    }, inplace=True)
    
    # 确保数据足够
    if len(live_data_df) < minutes_needed:
        print(f"警告: 数据不足，需要至少 {minutes_needed} 分钟, 当前只有 {len(live_data_df)} 分钟。")
        return None
        
    return live_data_df.tail(minutes_needed)

# ===================================================================
#      第3部分: 主预测函数
# ===================================================================

def make_prediction():
    """
    执行一次完整的实时预测流程
    """
    print("="*60)
    print("开始实时预测（已修复数据泄露问题）")
    print("="*60)
    
    # 1. 加载模型和配置
    print("加载模型和配置...")
    try:
        model = joblib.load('btc_prediction_model_fixed.joblib')
        with open('model_config_fixed.json', 'r') as f:
            config = json.load(f)
    except FileNotFoundError as e:
        print(f"错误：找不到修复版模型或配置文件！ {e}")
        print("请先运行 LightGBM_fixed.py 训练修复版模型。")
        return

    best_threshold = config['best_threshold']
    feature_list = config['feature_list']
    
    print(f"模型类型: {config.get('model_type', 'Unknown')}")
    print(f"数据泄露已修复: {config.get('data_leakage_fixed', False)}")
    print(f"最优阈值: {best_threshold:.3f}")
    
    # 2. 获取最新数据
    live_data_df = get_latest_data()
    if live_data_df is None:
        return
    
    print(f"获取到 {len(live_data_df)} 条历史数据")
    print(f"数据时间范围: {live_data_df.index.min()} 到 {live_data_df.index.max()}")
    
    # 3. 计算特征（无前瞻偏差）
    print("计算特征（严格避免前瞻偏差）...")
    features_df = calculate_features_no_lookahead_single(live_data_df.copy())
    
    # 检查是否有可用的数据行进行预测
    if features_df.empty:
        print("错误：计算特征后没有剩下任何数据。")
        return
    
    # 清理NaN值
    features_df_clean = features_df.dropna()
    if features_df_clean.empty:
        print("错误：清理NaN后没有剩下任何数据。")
        return
    
    print(f"特征计算完成，可用数据: {len(features_df_clean)} 条")
    
    # 4. 准备模型输入
    try:
        latest_features = features_df_clean[feature_list].iloc[-1:]
        current_price = features_df_clean.iloc[-1]['close']
        current_time = features_df_clean.index[-1]
    except KeyError as e:
        print(f"错误：缺少必要的特征列 {e}")
        print("可能是特征计算函数与训练时不一致。")
        return
    
    # 5. 进行预测
    print("模型正在预测...")
    try:
        probability = model.predict_proba(latest_features)[0, 1]
    except Exception as e:
        print(f"预测时发生错误: {e}")
        return
    
    # 6. 做出决策
    print("\n" + "="*20 + " 最终决策 " + "="*20)
    print(f"当前时间: {current_time}")
    print(f"当前价格: ${current_price:.2f}")
    print(f"预测10分钟后上涨的概率: {probability:.4f}")
    
    decision = "放弃"
    confidence_level = "信心不足"
    
    if probability > best_threshold:
        decision = "猜测【涨】"
        confidence_level = f"高信心 ({probability:.3f} > {best_threshold:.3f})"
    elif probability < (1 - best_threshold):
        decision = "猜测【跌】"
        confidence_level = f"高信心 ({probability:.3f} < {1-best_threshold:.3f})"
    else:
        confidence_level = f"信心不足 ({1-best_threshold:.3f} <= {probability:.3f} <= {best_threshold:.3f})"
    
    print(f"决策: {decision}")
    print(f"信心水平: {confidence_level}")
    print("="*54)
    
    # 7. 返回预测结果（供其他程序使用）
    return {
        'timestamp': current_time,
        'current_price': current_price,
        'probability': probability,
        'decision': decision,
        'threshold': best_threshold,
        'data_leakage_fixed': True
    }

# ===================================================================
#      第4部分: 特征一致性检查函数
# ===================================================================

def check_feature_consistency():
    """
    检查实时预测的特征计算是否与训练时一致
    """
    print("检查特征一致性...")
    
    try:
        with open('model_config_fixed.json', 'r') as f:
            config = json.load(f)
        expected_features = set(config['feature_list'])
    except FileNotFoundError:
        print("找不到配置文件，无法检查特征一致性")
        return False
    
    # 获取测试数据
    test_data = get_latest_data(100)
    if test_data is None:
        print("无法获取测试数据")
        return False
    
    # 计算特征
    features_df = calculate_features_no_lookahead_single(test_data.copy())
    features_df_clean = features_df.dropna()
    
    if features_df_clean.empty:
        print("测试数据计算特征后为空")
        return False
    
    actual_features = set(features_df_clean.columns)
    
    # 检查特征是否匹配
    missing_features = expected_features - actual_features
    extra_features = actual_features - expected_features
    
    if missing_features:
        print(f"缺少特征: {missing_features}")
        return False
    
    if extra_features:
        print(f"多余特征: {extra_features}")
    
    print("特征一致性检查通过！")
    return True

# ===================================================================
#      第5部分: 主程序入口
# ===================================================================

if __name__ == '__main__':
    # 可选：先检查特征一致性
    # if not check_feature_consistency():
    #     print("特征一致性检查失败，请检查代码")
    #     exit(1)
    
    # 执行预测
    result = make_prediction()
    
    if result:
        print(f"\n预测结果已返回: {result}")
    else:
        print("\n预测失败")
