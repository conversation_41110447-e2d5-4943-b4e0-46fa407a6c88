# debug_discrepancy.py
import pandas as pd
import joblib
import json
from feature import calculate_all_features # 确保从统一的模块导入

def debug_timestamp_discrepancy(target_timestamp_str):
    """
    针对一个特定的时间戳，对比两种上下文下的特征计算和模型预测结果。
    """
    print("="*60)
    print(f"🕵️  法医鉴定开始，目标时间戳: {target_timestamp_str}")
    print("="*60)

    # --- 1. 加载所有工具 ---
    try:
        model = joblib.load('btc_prediction_model.joblib')
        config = json.load(open('model_config.json', 'r'))
        full_df = pd.read_csv('btcusd_1-min_data.csv')
    except FileNotFoundError as e:
        print(f"错误：找不到必要文件！ {e}")
        return

    # --- 2. 准备基础数据 ---
    full_df['Timestamp'] = pd.to_datetime(full_df['Timestamp'], unit='s')
    full_df.set_index('Timestamp', inplace=True)
    full_df.rename(columns={'Open':'open', 'High':'high', 'Low':'low', 'Close':'close', 'Volume':'volume'}, inplace=True)
    full_df.sort_index(inplace=True)
    
    target_timestamp = pd.to_datetime(target_timestamp_str)
    feature_list = config['feature_list']

    # --- 3. 模拟 "train.py" 的上下文 (全知视角) ---
    print("\n--- 模拟 [train.py] 上下文 (使用全部历史数据) ---")
    # 为了计算特征，我们需要从数据开始到目标时间点的所有数据
    df_for_train_context = full_df.loc[:target_timestamp]
    features_train_context = calculate_all_features(df_for_train_context.copy())
    
    if target_timestamp not in features_train_context.index:
        print("错误：在 'train.py' 上下文中，目标时间戳的特征行在dropna后被删除。")
        return
        
    features_row_train = features_train_context.loc[target_timestamp][feature_list]
    prob_train = model.predict_proba(features_row_train.to_frame().T)[0, 1]
    print(f"预测信心: {prob_train:.6f}")
    
    # --- 4. 模拟 "backtest_simulator.py" 的上下文 (有限记忆) ---
    print("\n--- 模拟 [backtest_simulator.py] 上下文 (使用300分钟切片) ---")
    buffer_size = 300
    simulator_start_time = target_timestamp - pd.Timedelta(minutes=buffer_size - 1)
    df_for_simulator_context = full_df.loc[simulator_start_time:target_timestamp]
    
    features_simulator_context = calculate_all_features(df_for_simulator_context.copy())
    
    if features_simulator_context.empty or target_timestamp not in features_simulator_context.index:
        print("错误：在 'simulator' 上下文中，目标时间戳的特征行在dropna后被删除。")
        return

    features_row_simulator = features_simulator_context.loc[target_timestamp][feature_list]
    prob_simulator = model.predict_proba(features_row_simulator.to_frame().T)[0, 1]
    print(f"预测信心: {prob_simulator:.6f}")

    # --- 5. 逐一对比特征值，找出差异！ ---
    print("\n--- 逐一特征对比 ---")
    comparison = pd.DataFrame({
        'Train_Context': features_row_train,
        'Simulator_Context': features_row_simulator
    })
    comparison['Difference'] = abs(comparison['Train_Context'] - comparison['Simulator_Context'])
    
    # 只显示有差异的特征
    differing_features = comparison[comparison['Difference'] > 1e-9] # 1e-9是为了处理浮点数精度问题

    if differing_features.empty:
        print("\n✅ 所有特征值完全一致！问题可能出在其他地方（如模型文件或库版本）。")
    else:
        print("\n❌ 发现特征值不一致！第一个差异点很可能就是问题的根源：")
        print(differing_features)

if __name__ == '__main__':
    # =================================================================
    #  在这里输入你在 train.py 和 simulator.py 中看到信心不一致的那个时间点
    # =================================================================
    TARGET_TIMESTAMP = "2025-07-08 00:00:00"  # <--- 修改这个时间！
    
    debug_timestamp_discrepancy(TARGET_TIMESTAMP)