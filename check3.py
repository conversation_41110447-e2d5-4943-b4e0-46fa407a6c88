# debug_discrepancy_final.py
import pandas as pd
import joblib
import json
import warnings

warnings.simplefilter(action='ignore', category=FutureWarning)

from feature import calculate_all_features
import config

def debug_timestamp_discrepancy(target_timestamp_str):
    """
    【最终法医脚本】对比三种不同上下文的特征计算结果。
    """
    print("="*60)
    print(f"🕵️  终极法医鉴定开始，目标时间戳: {target_timestamp_str}")
    print("="*60)

    # --- 1. 加载所有工具和原始数据 ---
    raw_df = pd.read_csv(config.SOURCE_DATA_FILE)
    
    # --- 2. 一次性预处理 ---
    raw_df['Timestamp'] = pd.to_datetime(raw_df['Timestamp'], unit='s')
    raw_df.set_index('Timestamp', inplace=True)
    raw_df.rename(columns={'Open':'open', 'High':'high', 'Low':'low', 'Close':'close', 'Volume':'volume'}, inplace=True)
    if config.START_DATE:
        raw_df = raw_df.loc[config.START_DATE:]
    raw_df.sort_index(inplace=True)
    full_df = raw_df
    
    target_timestamp = pd.to_datetime(target_timestamp_str)
    model_config = json.load(open(config.CONFIG_OUTPUT_FILE, 'r'))
    feature_list = model_config['feature_list']

    # --- 3. 【A】正确模拟 "train.py" (黄金标准) ---
    print("\n--- (A) 黄金标准 [train.py] 上下文 (先计算, 后切片)...")
    df_with_all_features = calculate_all_features(full_df.copy())
    if target_timestamp not in df_with_all_features.index:
        print(f"错误：目标时间戳 {target_timestamp} 在特征计算后不存在。")
        return
    features_row_train = df_with_all_features.loc[target_timestamp][feature_list]
    print(f"--- [train.py] 特征向量 (时间: {target_timestamp}) ---")
    print(features_row_train.to_string())
    print("-" * 50)
    
    # --- 4. 【B】模拟旧版模拟器 (未对齐的切片) ---
    print("\n--- (B) 旧模拟器上下文 (先切片[未对齐], 后计算)...")
    buffer_size = 2000 # 使用一个大的缓冲区
    simulator_start_time_unaligned = target_timestamp - pd.Timedelta(minutes=buffer_size - 1)
    df_for_simulator_unaligned = full_df.loc[simulator_start_time_unaligned:target_timestamp]
    features_simulator_unaligned = calculate_all_features(df_for_simulator_unaligned.copy())
    features_row_simulator_unaligned = features_simulator_unaligned.loc[target_timestamp][feature_list]
    print(f"--- [旧模拟器] 特征向量 (时间: {target_timestamp}) ---")
    print(features_row_simulator_unaligned.to_string())
    print("-" * 50)

    # --- 5. 【C】模拟新版模拟器 (对齐的切片) ---
    print("\n--- (C) 新模拟器上下文 (先切片[已对齐], 后计算)...")
    buffer_start_time = target_timestamp - pd.Timedelta(minutes=buffer_size - 1)
    aligned_start_time = buffer_start_time.floor('15min') # <-- 解决方案的核心！
    df_for_simulator_aligned = full_df.loc[aligned_start_time:target_timestamp]
    features_simulator_aligned = calculate_all_features(df_for_simulator_aligned.copy())
    features_row_simulator_aligned = features_simulator_aligned.loc[target_timestamp][feature_list]
    print(f"--- [新模拟器] 特征向量 (时间: {target_timestamp}) ---")
    print(features_row_simulator_aligned.to_string())
    print("-" * 50)


    # --- 6. 最终的自动差异报告 ---
    print("\n--- 最终自动差异报告 ---")
    comparison = pd.DataFrame({
        'Golden_Standard (A)': features_row_train,
        'Old_Simulator (B)': features_row_simulator_unaligned,
        'New_Simulator (C)': features_row_simulator_aligned
    })
    
    # 比较 A 和 C
    diff_A_C = abs(comparison['Golden_Standard (A)'] - comparison['New_Simulator (C)'])
    differing_features_A_C = comparison[diff_A_C > 1e-9]

    if differing_features_A_C.empty:
        print("\n✅ 最终验证通过！新模拟器(C)与黄金标准(A)的特征计算完全一致！")
        print("   这证明了“对齐切片”是正确的解决方案。")
    else:
        print("\n❌ 最终验证失败！新模拟器(C)与黄金标准(A)仍然存在差异。")
        print("   有差异的特征如下:")
        print(differing_features_A_C)

    # 比较 A 和 B 以确认问题所在
    diff_A_B = abs(comparison['Golden_Standard (A)'] - comparison['Old_Simulator (B)'])
    differing_features_A_B = comparison[diff_A_B > 1e-9]
    if not differing_features_A_B.empty:
        print("\nℹ️  信息：旧模拟器(B)与黄金标准(A)存在差异（这是预期的），问题在于resample的边缘效应。")
        print("   第一个差异点:")
        print(differing_features_A_B.head(1))


if __name__ == '__main__':
    # 使用你之前发现问题的那个时间点
    TARGET_TIMESTAMP = "2025-07-08 00:01:00"
    
    debug_timestamp_discrepancy(TARGET_TIMESTAMP)