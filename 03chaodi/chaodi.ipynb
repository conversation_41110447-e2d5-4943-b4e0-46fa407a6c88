import pandas as pd
import numpy as np
import pandas_ta as ta
from tqdm import tqdm
import matplotlib.pyplot as plt

# --- 1. 加载数据 ---
# (这部分代码不变)
df = pd.read_csv('sui.csv')
df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s')
df.set_index('Timestamp', inplace=True)
df.sort_index(inplace=True)
df = df[~df.index.duplicated(keep='first')]

# --- 2. 定义目标变量 Y (抄底分数) - 修正后的高效计算方法 ---
# --- 参数定义 ---
# 假设数据是5分钟K线, 一天 = 24 * 60 / 5 = 288 周期
# 未来7天 = 288 * 7 = 2016 个周期
LOOK_FORWARD_PERIODS = 288 * 7

print("开始计算抄底分数，这可能需要几分钟...")

# 将需要用到的列转换为NumPy数组，极大提升速度
lows = df['Low'].to_numpy()
closes = df['Close'].to_numpy()
n = len(df)

# 创建一个空的数组来存放分数
scores = np.full(n, np.nan) # 使用 np.nan 作为默认值

# 使用高效的循环和NumPy操作
# 我们只计算那些有足够未来数据的点
for i in tqdm(range(n - LOOK_FORWARD_PERIODS)):
    # 当前的 Low 价
    current_low = lows[i]
    
    # 未来 N 个周期的收盘价窗口
    future_window = closes[i + 1 : i + 1 + LOOK_FORWARD_PERIODS]
    
    # 计算未来价格高于当前 'Low' 的比例 P
    higher_count = np.sum(future_window > current_low)
    p = higher_count / LOOK_FORWARD_PERIODS # 分母是固定的周期数
    
    # 根据公式计算分数
    score = (p - 0.5) * 2
    
    # 将计算出的分数存入数组
    scores[i] = score

# 将计算好的分数作为一个新列添加回DataFrame
df['bottom_score'] = scores

# 清理掉尾部那些无法计算分数的行 (值为NaN的行)
df.dropna(subset=['bottom_score'], inplace=True)

print("\n抄底分数计算完成!")
print("抄底分数分布预览:")
print(df['bottom_score'].describe())

# 绘制分数分布图，直观感受
df['bottom_score'].hist(bins=50)
plt.title('Distribution of Bottom Score')
plt.show()

# --- 3. 构建特征 X ---
df.ta.sma(length=20, append=True)
df.ta.ema(length=50, append=True)
df.ta.rsi(length=14, append=True)
df.ta.macd(fast=12, slow=26, signal=9, append=True)
df.ta.bbands(length=20, std=2, append=True)
df.ta.atr(length=14, append=True)
df.ta.obv(append=True)

df['taker_buy_ratio'] = df['TakerBuyBaseVolume'] / df['Volume']
df['dist_from_sma20'] = (df['Close'] / df['SMA_20']) - 1
df['high_roll_288'] = df['High'].rolling(288).max() # 1-day high
df['low_roll_288'] = df['Low'].rolling(288).min()   # 1-day low
df['dist_from_high_roll'] = (df['Close'] / df['high_roll_288']) - 1

# 例如，计算日线级别的移动平均线，并将其应用到分钟线数据上
# 先计算日线数据
df_daily = df.resample('D').agg({
    'Open': 'first', 'High': 'max', 'Low': 'min', 'Close': 'last', 'Volume': 'sum'
})
df_daily['sma_20_daily'] = ta.sma(df_daily['Close'], length=20)

# 将日线指标映射回分钟线 (使用 ffill 向前填充)
df = df.merge(df_daily[['sma_20_daily']], left_index=True, right_index=True, how='left')
df['sma_20_daily'].ffill(inplace=True)

# 创建一个新特征：当前价格与日线级别均线的偏离度
df['dist_from_daily_sma'] = (df['Close'] / df['sma_20_daily']) - 1


# 清理因计算指标产生的NaN
df.dropna(inplace=True)

# 准备最终的训练数据
features = [col for col in df.columns if col not in ['bottom_score', 'CloseTime']]
X = df[features]
y = df['bottom_score']

import xgboost as xgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# --- 4. 数据集划分 (时间序列) ---
# (这部分代码不变)
split_index = int(len(df) * 0.8)
X_train, X_test = X.iloc[:split_index], X.iloc[split_index:]
y_train, y_test = y.iloc[:split_index], y.iloc[split_index:]

# --- 5. 训练 XGBoost 回归模型 (修正) ---

# 注意：我们将 early_stopping_rounds 移到了这里！
model = xgb.XGBRegressor(
    objective='reg:squarederror', 
    eval_metric='rmse',
    n_estimators=1000,
    learning_rate=0.01,
    max_depth=5,
    subsample=0.8,
    colsample_bytree=0.8,
    random_state=42,
    n_jobs=-1,
    # 将 early_stopping_rounds 参数放在模型初始化中
    early_stopping_rounds=50 
)

# 现在 fit 方法中不再需要这个参数
print("开始训练模型...")
model.fit(X_train, y_train, 
          eval_set=[(X_test, y_test)],
          verbose=True # verbose可以保留，用于打印训练过程
         )

print("\n模型训练完成！")

# --- 6. 模型评估 ---
y_pred = model.predict(X_test)

rmse = np.sqrt(mean_squared_error(y_test, y_pred))
mae = mean_absolute_error(y_test, y_pred)
r2 = r2_score(y_test, y_pred)

print("\n--- 回归模型性能评估 ---")
print(f"均方根误差 (RMSE): {rmse:.4f}")
print(f"平均绝对误差 (MAE): {mae:.4f}")
print(f"R^2 分数: {r2:.4f}")

# MAE的解读：例如 MAE=0.2，表示模型预测的分数平均偏离真实分数0.2。
# R^2的解读：表示模型解释了目标变量（分数）方差的百分比。越高越好。

import matplotlib.pyplot as plt

# --- 7. 生成交易信号并可视化 ---
# 这是一个关键参数，需要你根据回测和风险偏好来调整
# 0.8 意味着我们想找一个点，模型预测在未来7天，90%的时间价格都会比它高
# (0.9 - 0.5) * 2 = 0.8
BUY_SCORE_THRESHOLD = 0.7

# 将预测分数添加到测试集的DataFrame中
test_df = df.iloc[split_index:].copy()
test_df['predicted_score'] = y_pred

# 找到模型发出买入信号的时间点
buy_signals = test_df[test_df['predicted_score'] >= BUY_SCORE_THRESHOLD]

# 绘图
plt.figure(figsize=(20, 8))
plt.plot(test_df.index, test_df['Close'], label='BTC Price', color='skyblue', alpha=0.8)
plt.scatter(buy_signals.index, buy_signals['Low'], marker='^', color='red', s=120, label=f'Buy Signal (Score >= {BUY_SCORE_THRESHOLD})', zorder=5)

# 为了对比，我们把真实的“好”买点也画出来
true_good_buys = test_df[test_df['bottom_score'] >= BUY_SCORE_THRESHOLD]
# plt.scatter(true_good_buys.index, true_good_buys['Low'], marker='o', color='green', s=50, alpha=0.7, label=f'True Good Buy (Score >= {BUY_SCORE_THRESHOLD})', zorder=4)

plt.title('BTC Price with Predicted Buy Signals vs. True Good Buys')
plt.legend()
plt.grid(True, linestyle='--', alpha=0.5)
plt.show()

# 在你生成 y_pred 之后，运行这行代码
predicted_scores_series = pd.Series(y_pred, index=y_test.index)

print("模型预测分数的统计描述:")
print(predicted_scores_series.describe())

# 检查训练集中 y_train 的分布
print("--- 训练集真实分数 y_train 的分布 ---")
print(y_train.describe())

# 绘制 y_train 的直方图
y_train.hist(bins=100)
plt.title('Distribution of True Scores in Training Set (y_train)')
plt.show()

# Define the thresholds for your categories. These can be tuned.
# We're saying anything with a score > 0.8 is a 'good buy'
# and anything < -0.8 is a 'bad buy'.
GOOD_BUY_THRESHOLD = 0.8
BAD_BUY_THRESHOLD = -0.8

# Create a function to apply the labels
def classify_score(score):
    if score >= GOOD_BUY_THRESHOLD:
        return 2  # 'GOOD_BUY'
    elif score <= BAD_BUY_THRESHOLD:
        return 0  # 'BAD_BUY'
    else:
        return 1  # 'NEUTRAL'

# Apply this function to create the new target column
df['trade_signal'] = df['bottom_score'].apply(classify_score)

# Check the distribution of our new target. It should be unbalanced, which is fine.
print("New Target Distribution:")
print(df['trade_signal'].value_counts())

# Our new features X remain the same
features = [col for col in df.columns if col not in ['bottom_score', 'trade_signal', 'CloseTime']] 
X = df[features]

# Our new target y is the categorical signal
y = df['trade_signal']

import xgboost as xgb
from sklearn.metrics import classification_report

# Split data (same as before)
split_index = int(len(df) * 0.8)
X_train, X_test = X.iloc[:split_index], X.iloc[split_index:]
y_train, y_test = y.iloc[:split_index], y.iloc[split_index:]

# --- Train a MULTICLASS XGBoost Classifier ---
# Note: XGBoost will automatically handle the class weights if you want,
# but for now let's start simple.
model = xgb.XGBClassifier(
    # The objective is now 'multi:softprob' which gives probabilities for each class
    objective='multi:softprob', 
    eval_metric='mlogloss',      # Evaluation metric for multiclass
    num_class=3,                 # We have 3 classes (0, 1, 2)
    n_estimators=500,
    learning_rate=0.05,
    # You can add the rest of your parameters (max_depth, etc.)
    early_stopping_rounds=50,    # Add this back in for the new XGBoost versions
    use_label_encoder=False      # Good practice
)

print("Starting Multiclass Model Training...")
model.fit(X_train, y_train, 
          eval_set=[(X_test, y_test)],
          verbose=True)

# This will return an array with shape (n_samples, 3)
# Each row contains the probability for class 0, 1, and 2 respectively.
y_pred_proba = model.predict_proba(X_test)

# To get the final predicted class, use .predict()
y_pred = model.predict(X_test)

print("\n--- Multiclass Classification Report ---")
print(classification_report(y_test, y_pred, target_names=['BAD_BUY (0)', 'NEUTRAL (1)', 'GOOD_BUY (2)']))

# --- TO FIND YOUR BUY SIGNALS ---
# You want to find where the model is confident about class 2 ('GOOD_BUY')
good_buy_probability = y_pred_proba[:, 2] # Probabilities for the 'GOOD_BUY' class

# Add this to your test dataframe
test_df = df.iloc[split_index:].copy()
test_df['good_buy_prob'] = good_buy_probability

# Set a probability threshold. Let's say we only trust signals with > 70% confidence.
PROB_THRESHOLD = 0.70
buy_signals = test_df[test_df['good_buy_prob'] >= PROB_THRESHOLD]

# Now, visualize these 'buy_signals' on your chart. You should see some results!

# --- Use Probabilities to Generate High-Confidence Signals ---

# This gives us the probability for each class [prob_bad, prob_neutral, prob_good]
y_pred_proba = model.predict_proba(X_test)

# We only care about the probability of our target class, 'GOOD_BUY' (which is the last column, index 2)
good_buy_probability = y_pred_proba[:, 2] 

# Add this to your test dataframe for analysis
test_df = df.iloc[split_index:].copy()
test_df['good_buy_prob'] = good_buy_probability

# --- THIS IS THE KEY ---
# Set a high threshold. Don't listen to the model unless it's very confident.
# Let's start with 70%. You can tune this parameter.
PROB_THRESHOLD = 0.70 

buy_signals = test_df[test_df['good_buy_prob'] >= PROB_THRESHOLD]

print(f"\nFound {len(buy_signals)} high-confidence buy signals with a threshold of {PROB_THRESHOLD}")

# Now, visualize these new, filtered 'buy_signals' on your chart.
# (Your visualization code for plotting the signals remains the same)
plt.figure(figsize=(20, 8))
plt.plot(test_df.index, test_df['Close'], label='BTC Price', color='skyblue', alpha=0.8)
if not buy_signals.empty:
    plt.scatter(buy_signals.index, buy_signals['Low'], marker='^', color='red', s=120, label=f'Confident Buy Signal (Prob >= {PROB_THRESHOLD})', zorder=5)
plt.title('BTC Price with High-Confidence Buy Signals')
plt.legend()
plt.grid(True, linestyle='--', alpha=0.5)
plt.show()

from sklearn.utils.class_weight import compute_class_weight

# Calculate weights before training the model
# This will give more weight to the classes with fewer samples
weights = compute_class_weight(class_weight='balanced', classes=np.unique(y_train), y=y_train)
# The output will be an array of weights, one for each class [w_bad, w_neutral, w_good]

# Then, you would ideally pass this to your model. 
# XGBoost doesn't have a direct `class_weight` param like scikit-learn,
# but for binary cases you use `scale_pos_weight`. For multiclass, it's more complex.
#
# **For now, focus on Action 1 and 2. They are more straightforward and often sufficient.**

# (Code from before)
y_pred_proba = model.predict_proba(X_test)
good_buy_probability = y_pred_proba[:, 2] 
test_df = df.iloc[split_index:].copy()
test_df['good_buy_prob'] = good_buy_probability

# --- INVESTIGATE THE PROBABILITIES ---
print("Statistics for the 'GOOD_BUY' probability predictions:")
print(test_df['good_buy_prob'].describe())

# --- FIND A REALISTIC THRESHOLD ---
# Let's see what the top 1% of predictions look like.
# This gives us a threshold that will guarantee we get some signals to analyze.
realistic_threshold = test_df['good_buy_prob'].quantile(0.99)
print(f"\nA more realistic threshold (99th percentile) would be: {realistic_threshold:.4f}")