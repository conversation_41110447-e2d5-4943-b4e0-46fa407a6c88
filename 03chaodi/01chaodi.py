# -*- coding: utf-8 -*-

# --- 第0步：导入所需库 ---
import pandas as pd
import numpy as np
import pandas_ta as ta
from tqdm import tqdm
import xgboost as xgb
import matplotlib.pyplot as plt
from sklearn.metrics import classification_report
from scipy.stats import linregress # 导入线性回归计算库

print("======================================================")
print("方案七：趋势斜率过滤器版")
print("======================================================")

# --- 第1步：数据加载与准备 ---
print(">>> 1. 正在加载数据...")
df = pd.read_csv('sui.csv')
df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s')
df.set_index('Timestamp', inplace=True)
df.sort_index(inplace=True)
df = df[~df.index.duplicated(keep='first')]

# --- 第2步：定义目标变量 ---
print(">>> 2. 正在计算目标变量 'future_return'...")
LOOK_FORWARD_PERIODS = 288 * 3
df['future_high'] = df['High'].rolling(window=LOOK_FORWARD_PERIODS, min_periods=1).max().shift(-LOOK_FORWARD_PERIODS)
df['future_return'] = (df['future_high'] / df['Close']) - 1
df.dropna(subset=['future_return'], inplace=True)

# --- 第3步：【核心改进1】为趋势识别和反转确认准备指标 ---
print(">>> 3. 正在构建特征和过滤器指标...")
# a) 基础指标 (给模型用)
df.ta.rsi(length=14, append=True)
df.ta.stoch(append=True)

# b) 反转确认指标 (给过滤器用)
df['SMA_5'] = ta.sma(df['Close'], length=5)
df['SMA_20'] = ta.sma(df['Close'], length=20)

# c) 趋势方向指标 (给过滤器用) - 计算滚动斜率
TREND_WINDOW = 100 # 定义趋势判断的窗口期
# 创建一个函数来计算斜率
def calculate_slope(series):
    # 我们需要一个x轴，就是时间序列的序号
    x = np.arange(len(series))
    # 计算线性回归，我们只需要斜率
    slope, _, _, _, _ = linregress(x, series)
    return slope

# 使用.rolling().apply()来计算每一根K线之前的趋势斜率
# 这个过程会比较慢，但结果非常精确
print(f">>> 3.5. 正在计算过去 {TREND_WINDOW} 周期的趋势斜率 (这步较慢)...")
df['trend_slope'] = df['Close'].rolling(window=TREND_WINDOW).apply(calculate_slope, raw=False)

df.dropna(inplace=True)

# --- 第4步：转化为二分类问题 ---
PROFIT_THRESHOLD = 0.05
print(f">>> 4. 正在将问题转化为二分类问题 (目标收益率 > {PROFIT_THRESHOLD*100}%)")
df['trade_signal'] = (df['future_return'] >= PROFIT_THRESHOLD).astype(int)

# --- 第5步：准备训练和测试数据 ---
print(">>> 5. 正在划分数据集...")
features = ['RSI_14', 'STOCHk_14_3_3']
X = df[features]
y = df['trade_signal']
split_index = int(len(df) * 0.8)
X_train, X_test = X.iloc[:split_index], X.iloc[split_index:]
y_train, y_test = y.iloc[:split_index], y.iloc[split_index:]

# --- 第6步：训练模型 ---
print(">>> 6. 正在训练XGBoost模型...")
model = xgb.XGBClassifier(objective='binary:logistic', eval_metric='logloss',
    n_estimators=1000, learning_rate=0.01, max_depth=5,
    early_stopping_rounds=50, use_label_encoder=False)
model.fit(X_train, y_train, eval_set=[(X_test, y_test)], verbose=False)

# --- 第7步：【核心改进2】应用全新的趋势斜率过滤器 ---
print(">>> 7. 正在应用趋势斜率过滤器...")
y_pred_proba = model.predict_proba(X_test)
good_trade_probability = y_pred_proba[:, 1]
test_df = df.iloc[split_index:].copy()
test_df['good_trade_prob'] = good_trade_probability

print("\n--- 'Good Trade' 预测概率的统计描述 ---")
print(test_df['good_trade_prob'].describe())

# 过滤器1：模型置信度过滤器
PROB_THRESHOLD = 0.60
model_signals = test_df[test_df['good_trade_prob'] >= PROB_THRESHOLD]
print(f"\n步骤1: 模型预测找到 {len(model_signals)} 个潜在信号 (Prob >= {PROB_THRESHOLD})")

# 过滤器2 & 3：趋势斜率 + 反转确认 过滤器
if not model_signals.empty:
    # 过滤条件1 (左侧趋势): 要求之前的趋势必须是下降的 (斜率为负)
    # 我们可以设定一个阈值，来过滤掉那些只是轻微下跌的情况
    is_downtrend = model_signals['trend_slope'] < -0.001 # 斜率的绝对值可以调整
    
    # 过滤条件2 (右侧反转): 5日线刚刚上穿20日线
    sma5_series = test_df['SMA_5']
    sma20_series = test_df['SMA_20']
    is_golden_cross = (sma5_series > sma20_series) & (sma5_series.shift(1) <= sma20_series.shift(1))
    
    # 应用所有过滤器
    final_signals = model_signals[is_downtrend & is_golden_cross.loc[model_signals.index]]
    print(f"步骤2: 趋势斜率过滤后，最终剩下 {len(final_signals)} 个信号")
else:
    final_signals = model_signals
final_signals = model_signals
# --- 第8步：结果可视化 ---
print(">>> 8. 正在生成结果图表...")
plt.figure(figsize=(20, 10))
plt.plot(test_df.index, test_df['Close'], label='BTC Price', color='skyblue', alpha=0.9, linewidth=1)
# 可以在图上画出均线，以验证信号
plt.plot(test_df.index, test_df['SMA_5'], label='SMA 5 (Short-term)', color='green', alpha=0.6, linewidth=1)
plt.plot(test_df.index, test_df['SMA_20'], label='SMA 20 (Mid-term)', color='red', alpha=0.6, linewidth=1)

if not final_signals.empty:
    plt.scatter(final_signals.index, final_signals['Low'], marker='*', color='gold', s=300, 
                label=f'最终趋势反转信号', zorder=5, edgecolors='black', linewidth=1)
plt.title(f'【方案七】BTC价格与趋势斜率过滤信号', fontsize=16)
plt.legend(fontsize=12)
plt.grid(True, linestyle='--', alpha=0.5)
plt.show()