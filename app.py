import pandas as pd
import plotly.graph_objects as go
from dash import Dash, dcc, html, Input, Output, callback_context
import numpy as np
import time
import os
from flask_caching import Cache

# 读取CSV数据
def load_data():
    df = pd.read_csv('btcusd_1-min_data.csv')

    # 检查时间戳是否在合理范围内
    current_year = pd.Timestamp.now().year
    future_timestamps = df['Timestamp'] > (current_year + 1) * 365 * 24 * 60 * 60

    if future_timestamps.any():
        print(f"警告: 发现{future_timestamps.sum()}个未来时间戳，将进行调整")
        # 假设这些时间戳是毫秒而不是秒
        df.loc[future_timestamps, 'Timestamp'] = df.loc[future_timestamps, 'Timestamp'] / 1000

    df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='s')
    df.set_index('Timestamp', inplace=True)

    # 确保数据按时间排序
    df = df.sort_index()

    return df

# 聚合数据到不同的时间周期
def aggregate_data(df, period):
    if period == '1min':
        return df

    # 根据不同的时间周期重采样数据
    resampled = None
    if period == '15min':
        resampled = df.resample('15min')
    elif period == '30min':
        resampled = df.resample('30min')
    elif period == '1h':
        resampled = df.resample('1h')
    elif period == '4h':
        resampled = df.resample('4h')
    elif period == '1d':
        resampled = df.resample('1d')

    # 聚合OHLCV数据
    aggregated = resampled.agg({
        'Open': 'first',
        'High': 'max',
        'Low': 'min',
        'Close': 'last',
        'Volume': 'sum'
    })

    return aggregated

# 初始化日期范围
def get_date_range():
    df = load_data()
    min_date = df.index.min()
    max_date = df.index.max()

    # 检查最大日期是否在未来
    now = pd.Timestamp.now()
    if max_date > now:
        print(f"警告: 最大日期 {max_date} 在未来，将使用2012-2013年的数据作为默认范围")
        # 使用2012年的数据作为默认范围
        start_date = pd.Timestamp('2012-01-01')
        end_date = pd.Timestamp('2013-01-01')
    else:
        # 默认显示最近30天的数据
        end_date = max_date
        start_date = end_date - pd.Timedelta(days=30)

    return min_date, max_date, start_date, end_date

# 初始化Dash应用
app = Dash(__name__, suppress_callback_exceptions=True)

# 设置缓存
cache = Cache(app.server, config={
    'CACHE_TYPE': 'filesystem',
    'CACHE_DIR': 'cache-directory',
    'CACHE_DEFAULT_TIMEOUT': 300  # 缓存过期时间，单位为秒
})

# 缓存数据加载函数
@cache.memoize(timeout=3600)  # 缓存1小时
def get_cached_data():
    start_time = time.time()
    df = load_data()
    print(f"数据加载耗时: {time.time() - start_time:.2f}秒")
    return df

# 获取日期范围
min_date, max_date, start_date, end_date = get_date_range()

# 确保日期范围在合理范围内
now = pd.Timestamp.now()
if start_date > now or end_date > now:
    print(f"警告: 日期范围在未来，将使用2017年1月的数据作为默认范围")
    start_date = pd.Timestamp('2017-01-01')
    end_date = pd.Timestamp('2017-01-15')  # 只使用15天的数据，减少数据量

# 应用布局
app.layout = html.Div([
    html.H1("BTC K线图查看器"),

    html.Div([
        html.Div([
            html.Label("选择时间周期:"),
            dcc.Dropdown(
                id='timeframe-dropdown',
                options=[
                    {'label': '1分钟', 'value': '1min'},
                    {'label': '15分钟', 'value': '15min'},
                    {'label': '30分钟', 'value': '30min'},
                    {'label': '1小时', 'value': '1h'},
                    {'label': '4小时', 'value': '4h'},
                    {'label': '1天', 'value': '1d'}
                ],
                value='1min'
            ),
        ], style={'width': '300px', 'display': 'inline-block', 'margin-right': '20px'}),

        html.Div([
            html.Label("选择日期范围:"),
            dcc.DatePickerRange(
                id='date-picker-range',
                min_date_allowed=min_date,
                max_date_allowed=now,  # 限制最大日期为当前日期
                start_date=start_date,
                end_date=end_date,
                calendar_orientation='horizontal',
            ),
        ], style={'width': '500px', 'display': 'inline-block'}),

        html.Div([
            html.Button('应用', id='apply-button', n_clicks=0,
                       style={'margin-left': '10px', 'background-color': '#4CAF50', 'color': 'white', 'border': 'none', 'padding': '10px 20px'}),
        ], style={'display': 'inline-block', 'vertical-align': 'bottom', 'margin-left': '10px'}),
    ], style={'margin': '20px'}),

    # 添加加载指示器
    dcc.Loading(
        id="loading-1",
        type="circle",
        children=[dcc.Graph(id='candlestick-chart')],
    ),

    # 添加性能指标显示
    html.Div(id='performance-stats', style={'margin': '20px', 'color': '#666', 'font-size': '0.8em'}),

    # 添加数据信息显示
    html.Div(id='data-info', style={'margin': '20px', 'padding': '10px', 'background-color': '#f8f9fa', 'border-radius': '5px'}),
])

# 回调函数更新图表
@app.callback(
    [Output('candlestick-chart', 'figure'),
     Output('data-info', 'children')],
    [Input('apply-button', 'n_clicks')],
    [Input('timeframe-dropdown', 'value'),
     Input('date-picker-range', 'start_date'),
     Input('date-picker-range', 'end_date')]
)
def update_chart(n_clicks, timeframe, start_date, end_date):
    start_time = time.time()

    # 使用缓存加载数据
    df = get_cached_data()
    print(f"原始数据大小: {len(df)}")
    print(f"原始数据时间范围: {df.index.min()} 到 {df.index.max()}")
    print(f"数据加载耗时: {time.time() - start_time:.2f}秒")

    # 如果用户没有选择日期范围，使用默认范围
    if start_date is None or end_date is None:
        # 使用2017年1月的数据作为默认范围
        start_date = pd.Timestamp('2017-01-01')
        end_date = pd.Timestamp('2017-01-15')  # 只使用15天的数据，减少数据量

    # 确保日期范围在合理范围内
    now = pd.Timestamp.now()
    if pd.Timestamp(start_date) > now or pd.Timestamp(end_date) > now:
        print("警告: 所选日期范围在未来，将使用默认范围")
        start_date = pd.Timestamp('2017-01-01')
        end_date = pd.Timestamp('2017-01-15')

    # 限制日期范围的跨度，避免加载过多数据
    date_span = pd.Timestamp(end_date) - pd.Timestamp(start_date)
    if date_span.days > 30:  # 如果日期跨度超过30天
        print(f"警告: 所选日期范围过大 ({date_span.days}天)，将限制为30天")
        end_date = pd.Timestamp(start_date) + pd.Timedelta(days=30)

    print(f"使用日期范围: {start_date} 到 {end_date}")

    # 过滤日期范围
    filtered_df = df.loc[start_date:end_date]

    # 确保数据不为空
    if len(filtered_df) == 0:
        print("警告: 所选日期范围内没有数据，将使用默认范围")
        # 使用2017年的数据作为备选
        start_date = pd.Timestamp('2017-01-01')
        end_date = pd.Timestamp('2017-01-15')
        filtered_df = df.loc[start_date:end_date]

    # 如果数据量仍然很大，进行采样
    if len(filtered_df) > 5000 and timeframe == '1min':
        sample_rate = max(1, len(filtered_df) // 5000)  # 确保最多5000个数据点
        print(f"数据量过大 ({len(filtered_df)}行)，采样率: 1/{sample_rate}")
        filtered_df = filtered_df.iloc[::sample_rate].copy()

    print(f"过滤后数据大小: {len(filtered_df)}")

    # 聚合数据
    agg_start_time = time.time()
    aggregated_df = aggregate_data(filtered_df, timeframe)
    print(f"聚合后数据大小: {len(aggregated_df)}")
    print(f"数据聚合耗时: {time.time() - agg_start_time:.2f}秒")

    if len(aggregated_df) > 0:
        print(f"聚合后数据样本: {aggregated_df.iloc[0:2]}")
    else:
        print("警告: 聚合后数据为空!")

    # 如果聚合后的数据仍然很大，进一步采样
    if len(aggregated_df) > 2000:
        sample_rate = max(1, len(aggregated_df) // 2000)  # 确保最多2000个数据点
        print(f"聚合后数据量仍然过大 ({len(aggregated_df)}行)，采样率: 1/{sample_rate}")
        aggregated_df = aggregated_df.iloc[::sample_rate].copy()

    # 创建K线图
    fig = go.Figure(data=[go.Candlestick(
        x=aggregated_df.index,
        open=aggregated_df['Open'],
        high=aggregated_df['High'],
        low=aggregated_df['Low'],
        close=aggregated_df['Close'],
        name='BTC/USD'
    )])

    # 添加成交量图表
    fig.add_trace(go.Bar(
        x=aggregated_df.index,
        y=aggregated_df['Volume'],
        name='成交量',
        marker_color='rgba(128, 128, 255, 0.5)',
        yaxis='y2'
    ))

    # 更新布局
    fig.update_layout(
        title=f'BTC/USD {timeframe} K线图',
        xaxis_title='时间',
        yaxis_title='价格 (USD)',
        xaxis_rangeslider_visible=True,  # 启用时间轴滑块
        template='plotly_dark',
        yaxis=dict(
            title='价格 (USD)',
            side='left',
            showgrid=True,
            gridcolor='rgba(255, 255, 255, 0.1)'
        ),
        yaxis2=dict(
            title='成交量',
            side='right',
            overlaying='y',
            showgrid=False
        ),
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        margin=dict(l=50, r=50, t=50, b=50),
        hovermode='x unified',
        dragmode='zoom',  # 启用拖动缩放
        newshape_line_color='cyan',
        modebar_add=['drawline', 'drawopenpath', 'drawclosedpath', 'drawcircle', 'drawrect', 'eraseshape']  # 添加绘图工具
    )

    # 设置X轴格式
    fig.update_xaxes(
        rangeslider_visible=True,  # 显示时间轴滑块
        rangeselector=dict(  # 添加时间范围选择器
            buttons=list([
                dict(count=1, label="1小时", step="hour", stepmode="backward"),
                dict(count=1, label="1天", step="day", stepmode="backward"),
                dict(count=7, label="1周", step="day", stepmode="backward"),
                dict(count=1, label="1月", step="month", stepmode="backward"),
                dict(step="all", label="全部")
            ])
        )
    )

    # 计算总处理时间
    total_time = time.time() - start_time
    print(f"总处理时间: {total_time:.2f}秒")

    # 创建数据信息显示
    data_info = [
        html.H4("数据统计信息"),
        html.P(f"时间范围: {start_date} 到 {end_date}"),
        html.P(f"时间周期: {timeframe}"),
        html.P(f"数据点数量: {len(aggregated_df)}"),
        html.P(f"处理时间: {total_time:.2f}秒"),
    ]

    if len(aggregated_df) > 0:
        price_change = ((aggregated_df['Close'].iloc[-1] - aggregated_df['Open'].iloc[0]) / aggregated_df['Open'].iloc[0]) * 100
        data_info.append(html.P(f"价格变化: {price_change:.2f}%",
                               style={'color': 'green' if price_change >= 0 else 'red', 'font-weight': 'bold'}))

        data_info.append(html.P(f"最高价: {aggregated_df['High'].max():.2f}, 最低价: {aggregated_df['Low'].min():.2f}"))
        data_info.append(html.P(f"总成交量: {aggregated_df['Volume'].sum():.2f}"))

    return fig, data_info

# 添加性能指标显示
@app.callback(
    Output('performance-stats', 'children'),
    [Input('candlestick-chart', 'figure')]
)
def update_performance_stats(_):
    # 获取当前内存使用情况
    try:
        import psutil
        process = psutil.Process(os.getpid())
        memory_usage = process.memory_info().rss / 1024 / 1024  # 转换为MB

        return [
            html.P(f"内存使用: {memory_usage:.1f} MB"),
            html.P(f"缓存目录: {os.path.abspath('cache-directory')}")
        ]
    except ImportError:
        return [
            html.P("未安装psutil，无法显示内存使用情况"),
            html.P(f"缓存目录: {os.path.abspath('cache-directory')}")
        ]

# 运行应用
if __name__ == '__main__':
    # 创建缓存目录
    if not os.path.exists('cache-directory'):
        os.makedirs('cache-directory')

    # 预热缓存
    print("预热数据缓存...")
    get_cached_data()

    # 启动应用
    print("启动应用服务器...")
    app.run_server(debug=True, threaded=True)
