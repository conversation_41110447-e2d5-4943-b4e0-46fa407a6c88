[2025-07-13 15:37:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117998.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:38:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118054.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:39:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117997.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:40:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117992.12), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:41:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117984.93), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:42:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117967.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:43:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117967.23), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:44:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117967.24), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:45:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117967.23), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:46:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117967.23), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:47:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117967.23), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:48:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117967.23), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:49:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117967.23), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:50:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117944.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:51:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117944.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:52:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117913.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:53:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117949.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:54:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117950.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:55:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117902.29), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:56:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117924.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:57:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117948.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:58:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117957.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 15:59:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117979.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 16:00:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117999.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 16:01:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118006.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 16:01:40] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-13 16:01:50] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-13 16:02:00] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-13 16:02:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118006.96), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 16:03:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117970.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 16:04:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117970.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 16:05:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117970.93), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-13 16:06:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117968.08), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
