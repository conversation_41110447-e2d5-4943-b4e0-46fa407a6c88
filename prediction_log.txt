[2025-07-12 16:57:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117801.84), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 16:57:07] 系统停止: 预测总结 | 数据: {'total_predictions': 0, 'successful': 0, 'failed': 0, 'timeout': 0, 'success_rate': 0.0}
[2025-07-12 16:58:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117838.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 16:59:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117890.32), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:00:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117944.53), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:01:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117918.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:02:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117896.6), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:03:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117896.6), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:03:52] 系统停止: 用户中断
[2025-07-12 17:03:53] 系统停止: 预测总结 | 数据: {'total_predictions': 0, 'successful': 0, 'failed': 0, 'timeout': 0, 'success_rate': 0.0}
[2025-07-12 17:04:03] 模型加载: 成功加载模型和配置
[2025-07-12 17:04:03] 数据获取: 初始数据获取成功，1000条记录
[2025-07-12 17:04:05] 新预测: pred_20250712_170400 - 先涨1% | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117881.15), 'up_target': np.float64(119059.96149999999), 'down_target': np.float64(116702.3385)}
[2025-07-12 17:04:14] 系统停止: 用户中断
[2025-07-12 17:04:20] 系统停止: 预测总结 | 数据: {'total_predictions': 1, 'successful': 0, 'failed': 0, 'timeout': 0, 'success_rate': 0.0}
[2025-07-12 17:04:23] 模型加载: 成功加载模型和配置
[2025-07-12 17:04:23] 数据获取: 初始数据获取成功，1000条记录
[2025-07-12 17:04:25] 新预测: pred_20250712_170400 - 先涨1% | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117881.16), 'up_target': np.float64(119059.9716), 'down_target': np.float64(116702.3484)}
[2025-07-12 17:05:12] 新预测: pred_20250712_170500 - 先涨1% | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117918.8), 'up_target': np.float64(119097.988), 'down_target': np.float64(116739.61200000001)}
[2025-07-12 17:06:54] 新预测: pred_20250712_170600 - 先涨1% | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117902.3), 'up_target': np.float64(119081.323), 'down_target': np.float64(116723.277)}
[2025-07-12 17:07:11] 新预测: pred_20250712_170700 - 先涨1% | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117890.8), 'up_target': np.float64(119069.708), 'down_target': np.float64(116711.892)}
[2025-07-12 17:07:19] 系统停止: 用户中断
[2025-07-12 17:07:26] 系统停止: 预测总结 | 数据: {'total_predictions': 4, 'successful': 0, 'failed': 0, 'timeout': 0, 'success_rate': 0.0}
[2025-07-12 17:07:34] 模型加载: 成功加载模型和配置
[2025-07-12 17:07:34] 数据获取: 初始数据获取成功，1000条记录
[2025-07-12 17:07:34] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117890.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:08:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117890.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:09:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117890.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:10:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117890.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:11:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117909.34), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:12:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117909.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:13:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117894.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:14:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117888.72), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:15:22] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 17:15:28] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117888.72), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:15:58] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118027.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:16:59] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118008.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:18:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117939.68), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:19:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117972.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:20:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117973.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:21:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117973.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:22:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117972.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:23:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118012.34), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:24:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118032.74), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:25:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118032.31), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:26:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118002.54), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:27:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117980.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:28:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117980.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:29:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117980.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:29:58] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117980.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:31:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117977.12), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:32:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117951.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:33:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117951.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:34:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117951.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:35:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117938.61), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:36:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117902.3), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:37:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117938.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:38:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117980.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:39:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118031.56), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:40:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(117989.53), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:41:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118040.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:42:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118040.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:43:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118027.31), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:43:59] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118011.53), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:45:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118054.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:46:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118023.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:47:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118014.54), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:48:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118040.89), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:49:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118040.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:49:45] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 17:50:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118060.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:51:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118072.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:52:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118130.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:53:10] 新预测: pred_20250712_175300 - 先涨1% | 数据: {'probability': np.float64(0.9238095238095239), 'price': np.float64(118199.99), 'up_target': np.float64(119381.9899), 'down_target': np.float64(117017.99010000001)}
[2025-07-12 17:53:35] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 17:54:13] 新预测: pred_20250712_175400 - 先涨1% | 数据: {'probability': np.float64(0.9238095238095239), 'price': np.float64(118199.83), 'up_target': np.float64(119381.82830000001), 'down_target': np.float64(117017.8317)}
[2025-07-12 17:54:38] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 17:55:00] 新预测: pred_20250712_175500 - 先涨1% | 数据: {'probability': np.float64(0.9238095238095239), 'price': np.float64(118184.39), 'up_target': np.float64(119366.2339), 'down_target': np.float64(117002.54609999999)}
[2025-07-12 17:55:41] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 17:55:59] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118083.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:57:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118122.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:58:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118160.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 17:59:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118158.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:00:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118158.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:00:38] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:00:59] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118085.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:02:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118081.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:03:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118080.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:04:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118035.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:05:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118064.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:06:14] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 18:06:20] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118060.16), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:07:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118089.24), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:08:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118054.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:09:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118006.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:10:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118006.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:11:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118001.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:12:00] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:12:10] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:12:20] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:12:30] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:12:40] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:12:51] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:13:01] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:13:11] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:13:21] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:13:31] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:13:41] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:13:51] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:14:01] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:14:11] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:14:21] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:14:26] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117931.67), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:14:57] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117980.94), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:15:58] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117987.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:16:59] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(118000.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:18:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118050.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:18:40] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 18:19:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118033.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:20:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118086.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:21:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118093.84), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:22:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118085.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:23:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118076.77), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:24:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118063.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:25:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118040.12), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:26:03] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 18:26:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118078.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:27:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118051.16), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:28:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118075.2), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:28:56] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118065.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:29:57] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118065.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:30:59] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118073.2), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:32:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118100.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:33:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118129.13), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:33:26] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 18:34:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118140.96), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:34:57] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 18:35:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118122.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:36:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118076.77), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:37:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118061.61), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:38:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118104.81), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:39:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.5227817745803356), 'price': np.float64(118100.42), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:40:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118100.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:40:43] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:40:53] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:41:03] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:41:13] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:41:23] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:41:33] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:41:43] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:41:53] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 18:41:59] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118100.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:43:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118100.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:44:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118100.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:45:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118100.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:46:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118088.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:47:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118100.43), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:48:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118105.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:49:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118084.47), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:50:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(118033.15), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:51:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(118023.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:52:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118064.31), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:53:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(118037.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:53:57] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(118048.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:54:58] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118068.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:55:59] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118068.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:57:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118068.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:58:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118089.13), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 18:59:16] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 18:59:21] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118089.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:00:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118077.32), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:01:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118083.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:02:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118083.05), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:03:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118106.2), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:04:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118057.64), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:05:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117995.43), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:06:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117968.08), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:07:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117968.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:08:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117945.93), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:09:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117961.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:10:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117966.34), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:10:44] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 19:10:59] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 19:11:14] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 19:11:19] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117976.1), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:12:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117940.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:13:16] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 19:13:22] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117930.37), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:14:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117944.33), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:15:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117944.33), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:16:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117936.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:17:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117933.67), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:18:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117943.58), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:19:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117948.24), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:20:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117959.57), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:20:55] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 19:21:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117975.35), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:22:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117975.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:23:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117999.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:24:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118020.93), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:25:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(118009.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:26:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(118009.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:27:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(118009.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:28:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118009.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:29:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118009.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:30:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118009.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:31:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118009.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:32:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117960.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:33:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117940.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:34:09] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 19:34:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117939.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:35:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117939.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:36:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117908.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:37:12] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 19:37:17] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117888.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:38:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117888.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:39:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117957.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:40:00] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 19:40:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117991.62), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:41:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(118021.09), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:42:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117750.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:43:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117683.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:44:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117730.43), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:45:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117704.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:46:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117738.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:47:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117739.09), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:48:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117755.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:49:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117739.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:50:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117764.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:51:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117744.08), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:52:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117747.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:53:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117771.96), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:54:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117805.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:55:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117813.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:56:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117801.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:57:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117764.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:58:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117787.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 19:59:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117782.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:00:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117810.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:00:54] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:01:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117827.74), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:02:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117827.74), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:03:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117827.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:04:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117861.18), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:05:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117834.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:06:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117834.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:07:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117789.08), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:08:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117781.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:09:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117748.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:10:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117762.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:10:45] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 20:11:16] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:11:26] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 20:11:31] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117731.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:12:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117731.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:13:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117731.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:14:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117749.33), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:15:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117779.51), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:16:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117736.84), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:17:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117727.85), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:18:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117710.29), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:19:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117694.51), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:20:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117721.63), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:21:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117694.51), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:22:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117710.27), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:23:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117676.83), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:23:55] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:24:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117700.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:25:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117664.84), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:25:27] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:26:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117694.07), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:27:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117770.78), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:28:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117760.02), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:29:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117737.16), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:30:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117667.96), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:31:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117709.33), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:32:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117590.21), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:33:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117676.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:34:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117629.61), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:35:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117552.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:36:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117571.65), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:37:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117593.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:38:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117492.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:39:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117536.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:40:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117509.76), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:40:55] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 20:41:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117550.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:42:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117533.66), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:43:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117491.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:44:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117507.41), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:45:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117496.81), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:46:16] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:46:21] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117532.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:47:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117538.06), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:48:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117455.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:49:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117380.1), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:50:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117387.17), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:50:51] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:51:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117493.53), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:52:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117451.19), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:52:38] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:53:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117455.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:54:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117478.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:55:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117494.28), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:56:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117476.07), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:56:27] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:57:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117466.13), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:57:43] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:58:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117450.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 20:59:15] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 20:59:20] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117509.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:00:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117490.38), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:01:07] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117509.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:02:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117435.32), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:03:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117434.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:04:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117487.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:05:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117453.4), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:06:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117508.37), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:07:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117555.52), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:08:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117508.45), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:09:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117468.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:10:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117471.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:11:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117471.9), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:12:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117441.79), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:12:29] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 21:13:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117401.82), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:14:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117448.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:15:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117463.11), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:16:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117437.73), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:17:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117406.17), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:18:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117412.02), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:19:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117396.24), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:20:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117340.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:21:20] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
[2025-07-12 21:21:25] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117370.95), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:22:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117360.5), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:23:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117339.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:24:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117388.91), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:25:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117406.03), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:26:00] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117400.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:27:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117325.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:28:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117319.08), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:29:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117308.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:30:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117304.7), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:31:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117361.12), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:32:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117483.6), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:33:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117444.01), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:34:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117428.37), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:35:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117420.36), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:36:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117343.94), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:37:14] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117320.8), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:38:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117362.49), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:39:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117423.97), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:40:02] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117409.99), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:40:58] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 21:41:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117360.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:42:04] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117321.67), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:43:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117302.92), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:44:06] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117288.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:45:08] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117340.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:46:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117280.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:47:10] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117335.05), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:47:50] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 21:48:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117442.98), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:49:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117330.85), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:50:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.3670940170940171), 'price': np.float64(117350.59), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:51:15] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117433.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:52:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117433.39), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:53:03] 预测完成: pred_20250712_175300 - 超时⏰ | 数据: {'direction': '先涨1%', 'probability': np.float64(0.9238095238095239), 'start_price': np.float64(118199.99), 'end_price': 117391.47, 'duration_minutes': 240, 'reason': '超时'}
[2025-07-12 21:53:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117391.47), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:54:05] 预测完成: pred_20250712_175400 - 超时⏰ | 数据: {'direction': '先涨1%', 'probability': np.float64(0.9238095238095239), 'start_price': np.float64(118199.83), 'end_price': 117430.22, 'duration_minutes': 240, 'reason': '超时'}
[2025-07-12 21:54:05] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117430.22), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:55:17] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 21:55:23] 预测完成: pred_20250712_175500 - 超时⏰ | 数据: {'direction': '先涨1%', 'probability': np.float64(0.9238095238095239), 'start_price': np.float64(118184.39), 'end_price': 117437.48, 'duration_minutes': 240, 'reason': '超时'}
[2025-07-12 21:55:23] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117437.48), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:56:09] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117481.55), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:57:11] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117468.57), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:58:07] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Read timed out. (read timeout=10)
[2025-07-12 21:58:12] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117463.75), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 21:59:13] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117457.04), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:00:01] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4852670349907919), 'price': np.float64(117509.76), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:01:03] 分析结果: 信心不足，放弃预测 | 数据: {'probability': np.float64(0.4111111111111111), 'price': np.float64(117480.0), 'threshold': 0.66, 'reason': '模型信心度未达到阈值要求'}
[2025-07-12 22:01:53] 错误: 获取最新数据失败: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=BTCUSDT&interval=1m&limit=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
